<?php

'yii2-only`;

	namespace app\controllers;
	use app\models\Email;
	use Yang;

`/yii2-only';


#yii2: done

class EmailController extends Controller
{
	public $mailer;
	public $EmailDebug = false; // true;	// E-mailküldés debug (részletes!)

	public function __construct()
	{
		$this->mailer = Yii::createComponent('application.extensions.PHPMailer.EMailer', /* $exceptions = */ true);
	}

	/**
	 * sendEmail(): E-mail küldés
	 *
	 * Esetleges hiba:
	 *		SMTP ERROR: Failed to connect to server: Nem hozható létre kapcsolat, mert a célszámítógép már visszautas<PERSON>tta a kapcsolatot. (10061) SMTP connect() failed. Exception: SMTP connect() failed.
	 *		MEGOLDÁS: <PERSON>z hálózat: login_linksys helyett login_guest2
	 *
	 * @param type $addresses		string ;-vel elválasztva, vagy array(név => email,) alakban!
	 * @param type $subject			tárgy
	 * @param type $message			üzenet, ha üres, akkor a view-t, majd a layout-ot tölti be!
	 * @param type $view
	 * @param type $layout
	 * @param type $vars			behelyettesített változók!
	 * @return boolean				Sikerült a küldés?
	 *
	 * @throws CException
	 */
	public function sendEmail($addresses, $subject, $message= false, $view= false, $layout= null, $vars= array(), $images = array())
	{
		$current_time = date('Y-m-d H:i:s');
		$ok = true;

		$email_settings = Yang::getParam('email');

		$email_send_log = new Email;
		$email_send_log->setIsNewRecord(true);
		$email_send_log->email_type	= $layout.'/'.$view;	// E-mail típusa (később!)

		$email_sending_error = '';
		$email_addresses	 = '';

		try {
			if ($this->EmailDebug)
			{
				// - Debug mód: / Debug output level.
				//	Options:
				//		0: no output
				//		1: commands
				//		2: data and commands
				//		3: as 2 plus connection status
				//		4: low level data output
				//$this->mailer->setDebugLevel(/* $level = */ 4);
				$this->mailer->SMTPDebug	 = 4;
				$this->mailer->Debugoutput	 = 'html'; // 'error_log' 'echo'
			}
			if (!$this->mailer->SetLanguage('hu', Yang::getBasePath().'/extensions/mailer/phpmailer/language/'))
			{
				throw new CException(Yii::t( __CLASS__, 'HIBA! Nyelv beállítása sikertelen!'));
			}
			$this->mailer->CharSet	 = $email_settings['charset'];
			$this->mailer->Host		 = $email_settings['host'];
			$this->mailer->Port		 = $email_settings['port'];
			if ($email_settings['is_smtp']) {
				$this->mailer->IsSMTP();
				$this->mailer->SMTPAuth   = $email_settings['smtp_auth'];
				$this->mailer->SMTPSecure = $email_settings['smtp_secure'];
			}
			$this->mailer->Username   = $email_settings['username'];
			$this->mailer->Password   = $email_settings['password'];

			$this->mailer->IsHTML($email_settings['is_html']);

			$from_email						 = $email_settings['from_email'];
			$this->mailer->Sender			 = $email_settings['sender'];
			$this->mailer->From				 = $from_email;
			$this->mailer->FromName			 = $email_settings['from_name'];

			$email_send_log->sender_name	= $this->mailer->FromName.'/'.$this->mailer->Sender;
			$email_send_log->sender_email	= $this->mailer->From;
			if ($email_settings['confirm_reading']) {
				$this->mailer->ConfirmReadingTo = $email_settings['confirm_reading'];
			}
			$this->mailer->AddReplyTo($email_settings['reply_to']);

			if (!is_array($addresses)) {
				$addresses= explode(';', $addresses);
			}
			$email_send_log->to_name	 = '';
			$email_send_log->to_email	 = '';
			$email_send_log->cc_name	 = '';
			$email_send_log->cc_email	 = '';
			$email_addresses			 = '';
			$email_count				 = 0;
			foreach ($addresses as $name => $email)
			{
				if (is_int($name)) {
					$name= $email;
				}
				if ($email_settings['charset']!='UTF-8') {
					$name= iconv('UTF-8', $email_settings['charset'], $name);
				}
				if (isset($_SERVER['SERVER_NAME']) && strpos($_SERVER['SERVER_NAME'], 'localhost') !== FALSE) {
					$name.= ' ('.$email.')';
					$email= '<EMAIL>';
				}
				if ($email_count==0) {
					$this->mailer->AddAddress($email, $name);
					$email_send_log->to_name		.= $name.';';
					$email_send_log->to_email		.= $email.';';
				} else {
					$this->mailer->AddCC($email, $name);
					$email_send_log->cc_name		.= $name.';';
					$email_send_log->cc_email		.= $email.';';
				}
				$email_addresses.= $name.' <'.$email.'>; ';
				$email_count++;
			}

			$this->mailer->Subject = $subject;

			if ($message) {
				$this->mailer->MsgHTML($message);
				$this->mailer->Body = $message;
			} else {
				$vars['subject']= $subject;
				$this->mailer->getView( $view, $vars, $layout );
			}

			if ($email_settings['charset']!='UTF-8') {
				$this->mailer->Subject= iconv('UTF-8', $email_settings['charset'], $this->mailer->Subject);
				$this->mailer->Body= iconv('UTF-8', $email_settings['charset'], $this->mailer->Body);
				$this->mailer->Body= str_replace('content="text/html; charset=UTF-8"', 'content="text/html; charset=ISO-5589-2"', $this->mailer->Body);
				$this->mailer->AltBody= iconv('UTF-8', $email_settings['charset'], $this->mailer->AltBody);
			}

			if ($this->EmailDebug) {
				print('E-mail címzettek:<br>');
					print($email_addresses.'<br>');
				print('E-mail tárgya:<br>');
					print($this->mailer->Subject.'<br>');
				print('E-mail tartalma:<br>');
					print($this->mailer->Body.'<br>');
				print('Hibaüzenet:<br>');
					print($this->mailer->ErrorInfo.'<br>');
			}
			
			if (!$this->mailer->Send()) {
				$email_sending_error= 'HIBA! ('.__LINE__.')';
			}

		} catch (phpmailerException $e) {
			$email_sending_error.= '<br> Error message: '.$e->errorMessage();
			$email_sending_error.= '<br> Host: '.$this->mailer->Host;
			$email_sending_error.= '<br><pre>';
			$email_sending_error.= print_r($this->mailer, true);
			$email_sending_error.= '</pre>';
			$ok= false;

		} catch (Exception $e) {
			$email_sending_error.= '<br> Exception: '.$e->getMessage();
			$ok= false;
		}

		if ($email_sending_error!='') {
			if ($this->EmailDebug) {
				print('HIBA e-mail küldésekor:<br>');
					print($email_sending_error.'<br>');
			}
			mail('<EMAIL>', '[HIBA] E-mail küldés hiba!',
					"E-mail küldés közben hiba!\n"
					."-Hiba: ".$email_sending_error."\n"
					."-Hibaüzenet: ".$this->mailer->ErrorInfo."\n"
					."-Címzett(ek): ".$email_addresses."\n"
					."-Tárgy: ".$this->mailer->Subject."\n"
					."-Szöveg: "."\n"
					.$this->mailer->Body."\n"
					."-Eredeti szöveg:\n"
					.$message."\n"
					);
		}

		$email_send_log->subject		= $this->mailer->Subject;
		$email_send_log->body			= $this->mailer->Body;
		$email_send_log->attachment		= '';
		$email_send_log->status			= $ok;
		$email_send_log->log			= '';
		if (!$ok) {
			$email_send_log->log .= "-Hiba: ".$email_sending_error."\n";
			$email_send_log->log .= "-Hibaüzenet: ".$this->mailer->ErrorInfo."\n";
		}
		$email_send_log->sent_by = Yang::userAvailable() ? userID() : get_class(Yii::app());
		$email_send_log->sent_on = $current_time;
		$email_send_log->save(false);
		return $ok;
	}
}