<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\components\MyActiveForm;
	use app\models\Status;
	use app\models\svm\Event;
	use app\models\svm\Visitor;
	use Yang;

`/yii2-only';


#yii2: done

class EventController extends Controller
{
	public function actionIndex() {
		$this->layout = "//event/layouts/event";

		$this->render("application.views.event.event", [

		]);
	}

	public function actionSaveRegistration() {
		$this->layout = "//layouts/ajax";

		$saveReg = true;

		$formData = requestParam('formData');
		$name = $formData["name"];
		$id = $formData["id"];
		$company = $formData["company"];
		$phone = isset($formData["phone"])?$formData["phone"]:"";
		$email = isset($formData["email"])?$formData["email"]:"";
		$visitor_role = isset($formData["visitorRole"])?$formData["visitorRole"]:"";
		$note = isset($formData["note"])?$formData["note"]:"";
		$mode = isset($formData["mode"]) ? $formData["mode"] : "finish";

		$email = str_replace(" ", "", $email);

		if (empty($name)) {
			$saveReg = false;
		}

		if ($saveReg) {
			$validator = new CEmailValidator;

			if (!empty($email) && !$validator->validateValue($email)) {
				$saveReg = false;
			}
		}

		$checked_optins = [];
		foreach ($formData as $key => $val) {
			if ((int)$val && substr($key, 0, 9) === "dynOptIn_") {
				$optin = str_replace("dynOptIn_", "", $key);
				$checked_optins[] = $optin;
			}
		}

		$vExists = Visitor::model()->findByAttributes(['visitor_id' => $id]);

		if (!$vExists) {
			$v = new Visitor;
			$v->client_name = $company;
			$v->visitor_name = $name;
			$v->phone = $phone;
			$v->email = $email;
			$v->note = $note;
			$v->valid_from = date("Y-m-d");
			$v->valid_to = "2020-01-01";
			$v->status = Status::PUBLISHED;
			$v->checked_optins = implode(";", $checked_optins);
			$v->visitor_role_id = $visitor_role;

			$valid = $v->validate();

			if ($valid && $saveReg) {
				$v->save();
			} else {

			}

			$visitor_id = $v->visitor_id;

			$vModel = $v;
		} else {
			$vExists->checked_optins = implode(";", $checked_optins);
			$vExists->visitor_role_id = $visitor_role;

			if ($note !== "") {
				$vExists->note = $note;
			}

			$valid = $vExists->validate();

			if ($valid && $saveReg) {
				$vExists->save();
			}

			$visitor_id = $vExists->visitor_id;

			$vModel = $vExists;
		}

		// kilépteti a visitort
		if ($valid) {
			$filter = "`visitor_id` = '$visitor_id' AND `status` = 5";
			Event::model()->updateAll(['status' => 6, 'visit_end' => date("Y-m-d H:i:s"),], $filter);

			$e = new Event;
			$e->terminal_id = 1;
			$e->visitor_id = $visitor_id;
			$e->employee_name = "MLBKT";
			$e->visit_planned_begin = date("Y-m-d H:i:s");
			$e->visit_planned_end = date("Y-m-d H:i:s", strtotime(date("Y-m-d H:i:s") . " + 1 day"));
			$e->visit_begin = date("Y-m-d H:i:s");
			$e->valid_from = date("Y-m-d");
			$e->status = ($mode === "save")? 4 : 5; // 4: mentett, 5: belépett!!!

			$valid = $e->validate();

			if ($valid) {
				$e->save();

				$status = [
					'status'	=> 1,
					'pkSaved'	=> $e->row_id,
					'error'		=> null,
				];
			} else {
				$error = MyActiveForm::_validate($e);

				$arr = (array) json_decode($error);
				$msg = "";

				foreach ($arr as $value) {
					foreach ($value as $val) {
						$msg .= $val . "<br/>";
					}
				}

				$status = [
					'status'	=> 0,
					'pkSaved'	=> null,
					'error'		=> $msg,
				];
			}
		} else {
			$error = MyActiveForm::_validate($vModel);

			$arr = (array) json_decode($error);
			$msg = "";

			foreach ($arr as $value) {
				foreach ($value as $val) {
					$msg .= $val . "<br/>";
				}
			}

			$status = [
				'status'	=> 0,
				'pkSaved'	=> null,
				'error'		=> $msg,
			];
		}

		echo json_encode($status);
	}

	public function actionAutocomplete() {
		$this->layout = "//layouts/ajax";

		$search = requestParam('search');

		if ($search === "*") {
			$search = "";
		}

		$SQL = "
			SELECT
				v.*,
				vr.`row_id` as visitor_role,
				IF(e.`row_id` IS NOT NULL AND e.`status` = 5, ' - Megjelent', '') AS logged_in
			FROM
				`visitor` v
			LEFT JOIN
				`event` e
					ON v.`visitor_id` = e.`visitor_id`
						AND e.`status` IN (1,2,5)
			LEFT JOIN
				`visitor_role` vr
					ON v.`visitor_role_id` = vr.`row_id`
						AND vr.`status` = ".Status::PUBLISHED."
			WHERE
					v.`visitor_name` LIKE '%%$search%%'
				AND NOW() BETWEEN v.`valid_from` AND IFNULL(v.`valid_to`, '".App::getSetting("defaultEnd")."')
			GROUP BY
				v.`visitor_id`
			ORDER BY
				v.`visitor_name`
		";

		$results = dbFetchAll($SQL);

		$resp = array();

		$i = 0;

		if (count($results)) {
			foreach ($results as $result) {
				$resp[$i]['id']				= $result["visitor_id"];
				$resp[$i]['value']			= $result["visitor_name"].$result["logged_in"];
				$resp[$i]['id_card']		= $result["id_card_number"];
				$resp[$i]['company']		= $result["client_name"];
				$resp[$i]['phone']			= $result["phone"];
				$resp[$i]['email']			= $result["email"];
				$resp[$i]['visitor_role']	= $result["visitor_role"];
				$resp[$i]['note']			= $result['note'];

				$i++;
			}
		}

		if (!count($resp)) {
			$resp[0]['id'] = '';
			$resp[0]['value'] = Dict::getValue("auto_complete_no_results_found");
		}

		$results = array(
			'data' => $resp
		);

		echo json_encode($results);
	}

	public function actionDynOptIn() {
		$this->layout = "//layouts/ajax";

		$visitor_id = requestParam('visitor_id');

		$SQL = "
			SELECT
				`checked_optins`
			FROM
				`visitor`
			WHERE
				`visitor_id` = '".$visitor_id."'
					AND `status` = ".Status::PUBLISHED."
					AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '".App::getSetting("defaultEnd")."')
		";

		$results = dbFetchAll($SQL);

		$checked_optins = [];
		if (isset($results[0]["checked_optins"])) {
			$checked_optins = explode(";", $results[0]["checked_optins"]);
		}
		Yang::log(json_encode($checked_optins), "log", "sys.vis");

		$SQL = "
			SELECT
				`row_id`,
				CONCAT(REPLACE(`date`,'-','. '),'. ',`optin_name`) as optin_name
			FROM
				`event_optins`
			WHERE
				`status` = ".Status::PUBLISHED."
			ORDER BY `date`, `start_time`
		";

		$results = dbFetchAll($SQL);

		foreach ($results as $res) {
			$id = $res["row_id"];
			$optin_name = $res["optin_name"];

			$hasThisOptin = false;
			foreach ($checked_optins as $optin) {
				if ((int)$id === (int)$optin) {
					$hasThisOptin = true;
				}
			}

			echo '<div class="dynOptInsContainer">
				<div class="checkboxForDynOptIn">
					<input type="checkbox" class="dynOptIn" id="dynOptIn_'.$id.'" name="formData[dynOptIn_'.$id.']" value="1" '.($hasThisOptin?'checked':'').'/>
					<label for="dynOptIn_'.$id.'"><span></span></label>
				</div>
				<div class="checkboxLabel">
					<label for="dynOptIn_'.$id.'">'.$optin_name.'</label>
				</div>
			</div>';
		}
	}

	public function actionGetVisitorRoles()
	{
		$html = "";

		$SQL = "
				SELECT
					*
				FROM
					`visitor_role`
				WHERE
					`status` = ".Status::PUBLISHED."
				ORDER BY `visitor_role_name`
				";

		$rows = dbFetchAll($SQL);

		if(is_array($rows) && count($rows) > 0) {
			for ($i = 0; $i < count($rows); $i++) {
				$html .= '<option value="'.$rows[$i]['row_id'].'">'.$rows[$i]['visitor_role_name'].'</option>';
			}
        }

		echo $html;
	}
}
