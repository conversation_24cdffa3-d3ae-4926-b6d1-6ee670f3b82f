<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Grid2\Grid2Controller;
	use app\models\AuthRole;
	use Yang;

`/yii2-only';


#yii2: done

class RoleController extends Grid2Controller
{
	public function __construct()
	{
		parent::__construct("role");
	}

	protected function G2BInit() {
		$this->LAGridDB->setModelName("AuthRole");

		parent::setControllerPageTitleId("page_title_role");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			false);
		$this->LAGridRights->overrideInitRights("search_header",		true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		false);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);

		$u = new AuthRole;
		$c = new CDbCriteria();
		if (!App::getRight(null,"su") && (int)App::getSetting("auth_role_customer_visibility")) {
			$c->condition = "`customer_visibility` = 1";
		}
		$this->LAGridDB->setModelSelection($u, $c);

		parent::G2BInit();
	}

	public function columns()
	{
		return array(
			'role_name'			=> array('export'=> true , 'col_type'=>'ed', 'width'=>'*'),
			'description'		=> array('export'=> true , 'col_type'=>'ed', 'width'=>'*'),
		);
	}
}
?>
