<?php

declare(strict_types=1);

Yang::loadComponentNamespaces('SchedulingAssistantWorkScheduleEdit');
Yang::loadComponentNamespaces('SchedulingAssistantExecutor');
Yang::loadComponentNamespaces('SchedulingAssistantRotation');
Yang::loadComponentNamespaces('SchedulingAssistantCore');
Yang::loadComponentNamespaces('SchedulingAssistant');
Yang::loadComponentNamespaces('WorkScheduleCore');
Yang::loadComponentNamespaces('Grid2Core');
Yang::loadComponentNamespaces('Employee');
Yang::loadComponentNamespaces('Core');

use Components\SchedulingAssistantWorkScheduleEdit\Descriptor\EditOneDayDialogDescriptor;
use Components\SchedulingAssistantWorkScheduleEdit\Director\ColumnsBuildingDirector;
use Components\SchedulingAssistantWorkScheduleEdit\Exception\InvalidTimeFormatException;
use Components\SchedulingAssistantWorkScheduleEdit\Handler\UsedSavedSavingFromDialogHandler;
use Components\SchedulingAssistantWorkScheduleEdit\Provider\EditOneDayDialogDataProvider;

final class SchedulingAssistantEditOneDayController extends Grid2Controller
{
    public const DIALOG_VIEW = 'application.views.SchedulingAssistant.schedulingAssistantEditOneDay.Grid2.dialog';
    public const EDIT_SCHEDULING_ASSISTANT_ONE_DAY_DIALOG_LAYOUT = '/Grid2/layouts/editSchedulingAssistantOneDayDialogLayout';
    public const EDIT_SCHEDULING_ASSISTANT_DIALOG_SESSION_KEY = 'editSchedulingAssistantDialog';
    public const EDIT_SCHEDULING_ASSISTANT_ONE_DAY_DIALOG_COLUMNS = 'editSchedulingAssistantOneDayDialog';
    public const SAVE_URL = '/SchedulingAssistant/schedulingAssistantEditOneDay/save';

    public const CONTROLLER_ID = 'SchedulingAssistant/schedulingAssistantEditOneDay';

    private EditOneDayDialogDescriptor $descriptor;

    public function actionIndex($layout = '//Grid2/layouts/indexLayout', $view = '/Grid2/index', $params = array())
    {
        $this->layout = self::EDIT_SCHEDULING_ASSISTANT_ONE_DAY_DIALOG_LAYOUT;

        $defaultParams = [
            "generateFrom" => requestParam('generate_from'),
            "dialogMode" => (int)requestParam('dialogMode'),
            "editPK" => requestParam('editPK'),
            "actionSaveUrl" => self::SAVE_URL,
            "gridID" => requestParam('gridID'),
            "defaults" => requestParam('defaults'),
            "dialogMaxWidth" => $this->getDialogMaxWidth(),
            "lang" => requestParam('lang'),
        ];

        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $employeeContractId = requestParam('employeeContractId');
        $selectedDate = requestParam('startDate');
        $groupValue = requestParam('groupValue');

        $provider = new EditOneDayDialogDataProvider();
        $this->descriptor = $provider->provide(
            $selectedDate,
            $employeeContractId,
            $groupValue
        );
        $additionalParams['descriptor'] = $this->descriptor;

        $_SESSION[self::EDIT_SCHEDULING_ASSISTANT_DIALOG_SESSION_KEY] = [
            'employeeContractId' => $employeeContractId,
            'startDate' => $selectedDate,
            'endDate' => requestParam('endDate'),
        ];

        $params = Yang::arrayMerge($additionalParams, $defaultParams);

        $this->render(self::DIALOG_VIEW, $params);
    }

    public function columns() {
        $this->enableMultiGridMode();
        $columns[self::EDIT_SCHEDULING_ASSISTANT_ONE_DAY_DIALOG_COLUMNS] = (new ColumnsBuildingDirector())->build(
            $this->descriptor
        );

        return $columns;
    }

    public function actionSave(
        $data = array(),
        $modelName = null,
        $pk = null,
        $vOnly = false,
        $ret = false,
        $contentId = null
    ) {
        try {
            $dialogData = requestParam(
                'dialogInput_' . self::EDIT_SCHEDULING_ASSISTANT_ONE_DAY_DIALOG_COLUMNS
            );
            (new UsedSavedSavingFromDialogHandler())($dialogData);

            $response = [
                'status' => 1,
            ];
        } catch (InvalidTimeFormatException $e) {
            $response = [
                'status' => 0,
                'error' => $e->getMessage(),
            ];
        }

        echo json_encode($response);
    }

    public function actionDropdown(): string
    {
        return '{}';
    }
}
