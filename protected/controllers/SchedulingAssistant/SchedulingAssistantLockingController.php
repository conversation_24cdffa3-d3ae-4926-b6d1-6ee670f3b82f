<?php

declare(strict_types=1);

Yang::loadComponentNamespaces('SchedulingAssistantWorkScheduleEdit');
Yang::loadComponentNamespaces('SchedulingAssistantExecutor');
Yang::loadComponentNamespaces('SchedulingAssistantCore');
Yang::loadComponentNamespaces('SchedulingAssistant');
Yang::loadComponentNamespaces('WorkScheduleCore');
Yang::loadComponentNamespaces('Grid2Core');
Yang::loadComponentNamespaces('Core');

use Components\SchedulingAssistant\Provider\SchedulingAssistantEmployeeContractIdsByFilterProvider;
use Components\SchedulingAssistantCore\Builder\LockableWorkScheduleWithEmployeeRawDataBuilder;
use Components\SchedulingAssistantCore\Handler\LockingHandler;
use Components\SchedulingAssistantCore\Handler\UnlockingHandler;

final class SchedulingAssistantLockingController extends Grid2Controller
{
    public const CONTROLLER_ID = 'SchedulingAssistant/schedulingAssistantLocking';

    public function actionLock(): void
    {
        if (is_null(Yang::session('schedulingAssistant_activeEmployees'))) {
            Yang::log("SESSION timed out...", 'log', 'system.SchedulingAssistant.LocalMenu');
            return;
        }
        if (!App::hasRight($this->getControllerID(), "localmenu_lock")) {
            return;
        }

        [$startDate, $endDate, $employeeContractIds, $groupValue] = $this->handleRequestParams();
        $lockableWorkScheduleDescriptor = (new LockableWorkScheduleWithEmployeeRawDataBuilder())
            ->reset()
            ->setEmployeeContractIds($employeeContractIds)
            ->setStartDate($startDate)
            ->setEndDate($endDate)
            ->enableSetUsedSavedsWithoutStatus()
            ->enableSetWorkSchedule()
            ->enableSetAbsences()
            ->setGroupValueFromFilter($groupValue)
            ->build();

        $connection = (\Yang::app())->getDb();
        $transaction = $connection->beginTransaction();
        (new LockingHandler())($lockableWorkScheduleDescriptor);
        $transaction->commit();
    }

    public function actionUnlock(): void
    {
        if (is_null(Yang::session('schedulingAssistant_activeEmployees'))) {
            Yang::log("SESSION timed out...", 'log', 'system.SchedulingAssistant.LocalMenu');
            return;
        }

        if (!App::hasRight($this->getControllerID(), "localmenu_unlock")) {
            return;
        }

        [$startDate, $endDate, $employeeContractIds, $groupValue] = $this->handleRequestParams();

        $lockableWorkScheduleDesciptor = (new LockableWorkScheduleWithEmployeeRawDataBuilder)
            ->reset()
            ->setEmployeeContractIds($employeeContractIds)
            ->setStartDate($startDate)
            ->setEndDate($endDate)
            ->enableSetUsedSavedsWithoutStatus()
            ->enableSetWorkSchedule()
            ->enableSetAbsences()
            ->setGroupValueFromFilter($groupValue)
            ->build();

        $connection = (\Yang::app())->getDb();
        $transaction = $connection->beginTransaction();
        (new UnlockingHandler())($lockableWorkScheduleDesciptor);
        $transaction->commit();
        echo json_encode(
            [
                'startDate' => $startDate->format('Y-m-d'),
                'endDate' => $endDate->format('Y-m-d'),
                'employeeContractId' => reset($employeeContractIds),
                'dialogTitle' => Dict::getValue("edit_scheduling_assistant_one_day"),
                'groupValue' => $groupValue
            ]
        );
    }

    private function handleRequestParams(): array
    {
        $params = requestParam('schedulingAssistantParams');
        $groupValue = $this->provideGroupValue($params);
        $startDate = $this->provideStartDate($params);
        $endDate = $this->provideEndDate($params, $startDate);
        $employeeContractIds = $this->provideEmployeeContractIds($params);

        return [$startDate, $endDate, $employeeContractIds, $groupValue];
    }

    private function provideGroupValue(array $params): string
    {
        return $params['groupValue'] ?? '';
    }

    private function provideStartDate(array $params): DateTime
    {
        if (!isset($params['startDate'])) {
            throw new InvalidArgumentException(); #@TODO
        }
        return new DateTime($params['startDate']);
    }

    private function provideEndDate(array $params, DateTime $startDate): DateTime
    {
        if (isset($params['endDate'])) {
            $endDate = new DateTime($params['endDate']);
        } else {
            $endDate = clone $startDate;
        }
        return $endDate;
    }

    private function provideEmployeeContractIds(array $params): array
    {
        if (!isset($params['ecID'])) {
            throw new InvalidArgumentException(); #@TODO
        }

        if ($params['ecID'] === 'ALL') {
            $employeeContractIds = $this->getEmployeeContractIdsByGroupValue($params);
        } else {
            $employeeContractIds = [ $params['ecID'] ];
        }
        return $employeeContractIds;
    }

    private function getEmployeeContractIdsFromSession(): array
    {
        $activeEmployees = Yang::session('schedulingAssistant_activeEmployees');

        return array_column($activeEmployees, 'employee_contract_id');
    }

    private function getEmployeeContractIdsByGroupValue(array $params): array
    {
        $provider = new SchedulingAssistantEmployeeContractIdsByFilterProvider();
        $validFrom = $params['startDate'];
        $validTo = $params['startDate'];
        $groupValue = $params['groupValue'];

        return $provider->provide($validFrom, $validTo, $groupValue);
    }

}
