<?php

declare(strict_types=1);

Yang::loadComponentNamespaces('SchedulingAssistantWorkScheduleEdit');
Yang::loadComponentNamespaces('SchedulingAssistantCore');
Yang::loadComponentNamespaces('SchedulingAssistant');
Yang::loadComponentNamespaces('Grid2Core');
Yang::loadComponentNamespaces('Core');

final class SchedulingAssistantWorkSchedule extends Grid2Controller
{
    public const CONTROLLER_ID = 'SchedulingAssistant/schedulingAssistantWorkSchedule';

    private $baseLineHeight = 18;

    public function actionIndex(): void
    {
    }

    protected function fetchGridDataMain($filter, $cellMode = false, $diagMode = false): array
    {
        /**
         * - WorkSchedule
         *    - status: 2, 5, 6
         * - SchedulingAssistantUsedSaved
         *    - status: 2, 6
         * - EmployeeAbsence
         *     - employee_contract_id
         *     - day
         * - PublicHoliday
         *     - holidaydate
         *     - country?
         * - used template events
         */
        $topLineHeight = 0;
        $topLineNumber = 0;
        if ($this->getMode() === Grid2SchedulingAssistantController::AS_MODE && $diagMode) {
            $topLineHeight = 40;
        }

        $employees = Yang::session('schedulingAssistant_activeEmployees');
        $employeeCount = count($employees);

        $currentPage = requestParam('currentPage');

        if (empty($currentPage) || $currentPage === "null") {
            $currentPage = 0;
        }
        $lineHeight = $this->getLineHeight(null);

        $gridParams = requestParam('gridParams');
        $objboxHeight = $gridParams['objboxHeight'] - 18 - ($topLineHeight * $topLineNumber);

        $linesPerPage = (int)floor($objboxHeight / ($lineHeight /*+1*/)); // +1 for cell border
        $linesPerPage = $linesPerPage < 1 ? 1 : $linesPerPage;

        $pageStart = $currentPage * $linesPerPage;

        $pageCount = (int)ceil($employeeCount / $linesPerPage);

        $retArr = [];
        $retArr['data'] = [];
        $retArr['pageCount'] = $pageCount;
        $retArr['currentPage'] = $currentPage;
        $retArr['pageStart'] = $pageStart;
        $retArr['pageEnd'] = $pageStart + $linesPerPage;
        $retArr['totalRowCount'] = $employeeCount;
        $retArr['log'] = "***SchedulingAssistant***";
        $retArr['data'][] = [
            'columns' => [
                'employee' => [
                    'data' => 'html...',
                    'width' => 150,
                    'lineHeight' => 54,
                ],
                'name' => [
                    'data' => 'html...',
                    'width' => 31,
                    'lineHeight' => 54,
                ],
                'day_X' => [
                    'id' => 'ecID_date',
                    'data' => 'html...',
                    'width' => 120,
                    'lineHeight' => 54,
                ],
            ],
        ];

        return $retArr;
    }

    protected function getLineHeight($gridID)
    {
        $controllerID = $this->getControllerID();
        $worktimeLineEnabled = \App::hasRight($controllerID, "worktime_line");
        $overtimeLineEnabled = \App::hasRight($controllerID, "overtime_line");
        $baseLineHeight = $this->baseLineHeight;

        $lines = 1;
        $lineHeight = $baseLineHeight;

        if ($worktimeLineEnabled) {
            $lines++;
            $lineHeight += $baseLineHeight;
        }
        if ($overtimeLineEnabled) {
            $lines++;
            $lineHeight += $baseLineHeight;
        }

        if ($lines < 3) { // minimum 3 lines!
            $lineHeight += (3 - $lines) * $baseLineHeight;
        }

        return $lineHeight;
    }
}
