<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\API\GetActiveEmployeeAllData;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\models\Employee;
	use app\models\EmployeeTeam;
	use app\models\Status;
	use app\models\Team;
	use yii\web\Response;
	use Yang;

`/yii2-only';


#yii2: done

class TeamController extends Grid2Controller
{
	private $types;
	private $notUseApprover=Array('companyMainData');

	public function __construct()
	{
		parent::__construct("team");

		$this->types=Team::getTypes();
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("Team");

		parent::G2BInit();
	}

	public function columns()
	{
		return array(
			'team_type'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'combo', 'edit' => false, 'width' => 250,
													'options'		=>	array('mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'=>Yang::arrayMerge(array(array('id'=>NULL,'value'=>' ')),$this->types))),
			'owner_user_id'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'combo','window' => App::hasRight('team','use_every_team'),'width' => 250,
											'options'			=>	array(
														'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
														'sql'			=> "SELECT
																				u.`user_id` as id,
																				IF(".Employee::getParam('fullname_with_emp_id_ec_id',["e","ec"])." is NULL,u.`username`,".Employee::getParam('fullname_with_emp_id_ec_id',["e","ec"]).") as value
																			FROM `user` u
																			LEFT JOIN `employee` e ON
																					e.`employee_id`=u.`employee_id`
																				AND e.`status`=".Status::PUBLISHED."
																				AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."')
																			LEFT JOIN `employee_contract` ec ON
																					ec.`employee_id`=u.`employee_id`
																				AND ec.`status`=".Status::PUBLISHED."
																				AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '".App::getSetting("defaultEnd")."')
																				AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '".App::getSetting("defaultEnd")."')
																			WHERE
																					u.`status` = ".Status::PUBLISHED."
																			ORDER BY value",
														'array'			=> array(array("id"=>"","value"=>""))),),
			'team_name'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 250),
			'minimal_team_count'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 250),
			'note'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 250),
		);
	}

	public function attributeLabels()
	{
		return array(
			'owner_user_id'		=> Dict::getValue("user"),
			'team_name'			=> Dict::getValue("team_name"),
			'minimal_team_count'=> Dict::getValue("minimal_team_count"),
			'note'				=> Dict::getValue("note"),
		);
	}

	public function actionIndex($layout = '//Grid2/layouts/indexLayout', $view = '/Grid2/index', $params = array())
	{
		$this->layout = '//Grid2/layouts/indexLayout';

		$approverOptions=null;

		if(App::getSetting("team_change_approver_process")==='1')
		{
			$SQL="
				SELECT DISTINCT
					al.`lookup_value` as id,
					d.`dict_value` as value
				FROM `app_lookup` al
				LEFT JOIN `dictionary` d ON
						d.`dict_id`=al.`dict_id`
					AND d.`valid`=1
					AND d.`lang`='".Dict::getLang()."'
				LEFT JOIN `approver` a ON
						a.`process_id`=al.`lookup_value`
					AND a.`status`=".Status::PUBLISHED."
					AND a.`approver_user_id`='".userID()."'
					AND CURDATE() BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, '".App::getSetting("defaultEnd")."')
				WHERE
						al.`lookup_id`='approver_process_ids'
					AND al.`valid`=1
					AND al.`lookup_value` NOT IN ('".implode("','",$this->notUseApprover)."')
					AND a.`row_id` IS NOT NULL
			";
			$approverOptions = dbFetchAll($SQL);
		}

		$this->render('index', array(
			'title'			=>Dict::getValue("page_title_team"),
			'tree'			=>Dict::getValue("teams"),
			'associated'	=>Dict::getValue("associated_employees"),
			'available'		=>Dict::getValue("available_employees"),
			'approverOptions'=>$approverOptions,
		));
	}

	public function actionGenerateTreeView()
	{
		if (stristr($_SERVER["HTTP_ACCEPT"], "application/xhtml+xml"))
		{
			header("Content-type: application/xhtml+xml");
		}
		else
		{
			header("Content-type: text/xml");
		}

		$xml='<?xml version="1.0" encoding="utf-8" ?>'."\n";
		$xml.='<tree id="0">'."\n";

		$select_id= requestParam('selectedID');
		$pre_team=null;
		if(is_numeric($select_id))
		{
			$pre_team=Team::model()->findByAttributes(array('team_id'=>$select_id,'status' => Status::PUBLISHED));
		}

		for($i=0;$i<count($this->types);++$i)
		{

			$xml.='<item id="type_'.$this->types[$i]['id'].'" text="'.$this->types[$i]['value'].'" open="1" im0="folderClosed.gif" im1="folderOpen.gif" im2="folderClosed.gif">'."\n";

			$teams=$this->getTeams($this->types[$i]['id']);

			if(is_array($teams) && count($teams) > 0) {
				foreach($teams as $team)
				{

					$selectTeam=(!empty($pre_team) && $pre_team->team_id==$team['id'])?' select="1" ':'';

					$xml.='<item id="'.$team['id'].'" text="'.$team['value'].'" '.$selectTeam.' im0="book.gif" im1="book.gif" im2="book.gif" >'."\n";
					$xml.="</item>\n";
				}
			}
			$xml.="</item>\n";
		}
		$xml.="</tree>\n";
		echo($xml);
	}

	public function actionTypeOptions()
	{
		if (stristr($_SERVER["HTTP_ACCEPT"], "application/xhtml+xml")) {
			header("Content-type: application/xhtml+xml");
		} else {
			header("Content-type: text/xml");
		}
		$xml='<?xml version="1.0" encoding="utf-8" ?>'."\n";
		$xml.='<menu id="0">'."\n";
		$xml.='<item id="add" text="'.Dict::getValue("add").'" />'."\n";
		$xml.="</menu>\n";

		echo($xml);
	}

	public function actionTeamOptions()
	{
		if (stristr($_SERVER["HTTP_ACCEPT"], "application/xhtml+xml")) {
			header("Content-type: application/xhtml+xml");
		} else {
			header("Content-type: text/xml");
		}
		$xml='<?xml version="1.0" encoding="utf-8" ?>'."\n";
		$xml.='<menu id="0">'."\n";
		$xml.='<item id="modify" text="'.Dict::getValue("modify").'" />'."\n";
		$xml.='<item id="delete" text="'.Dict::getValue("delete").'" />'."\n";
		if(App::hasRight('team','share'))
		{
			$xml.='<item id="share" text="'.Dict::getValue("share").'" />'."\n";
		}
		$xml.="</menu>\n";

		echo($xml);
	}

	public function actionLoadAvailableEmployee()
	{
		$select_id         = requestParam('selectedID');
		$approver_filter	= App::getSetting("team_change_approver_process")==='1'?
				requestParam('approver_filter'):App::getSetting("team_default_approver_process");
		$available_filter  = requestParam('available_filter');

		$resp= array();

		$resp['status']=0;

		if($select_id!==NULL && strpos($select_id,'type_')===\FALSE)
		{
			$resp['status']=1;

			$SQL=$this->getEmployeeTeamSQL($select_id,$approver_filter);
			$SQL.="WHERE
						et.row_id IS NULL
				HAVING `value` like '%$available_filter%'
				ORDER BY `value`
			";

			$rows = dbFetchAll($SQL);

			$resp['data']=$rows;
		}


		echo json_encode($resp);
	}

	public function actionLoadAssociatedEmployee()
	{
		$select_id         = requestParam('selectedID');
		$approver_filter	= App::getSetting("team_change_approver_process")==='1'?
				requestParam('approver_filter'):App::getSetting("team_default_approver_process");
		$associated_filter = requestParam('associated_filter');

		$resp= array();

		$resp['status']=0;

		if($select_id!==NULL && strpos($select_id,'type_')===\FALSE)
		{
			$resp['status']=1;

			$SQL=$this->getEmployeeTeamSQL($select_id,$approver_filter);
			$SQL.="WHERE
						et.row_id IS NOT NULL
				HAVING `value` like '%$associated_filter%'
				ORDER BY `value`
			";

			$rows = dbFetchAll($SQL);

			$resp['data']=$rows;
		}


		echo json_encode($resp);
	}

	public function actionAssignTeam()
	{
		$from = requestParam('from');
		$to = requestParam('to');
		$ecId = requestParam('ecId');
		$select_id = requestParam('selectedID');
		$user_id=userID();

		$resp= array();

		$resp['status']=0;

		$team = Team::model()->findByAttributes(array('team_id'=>$select_id,'status' => Status::PUBLISHED));

		if($from!==$to && !empty($user_id) && !empty($team))
		{

			if($from==="available_table")
			{
				$SQL="INSERT INTO `employee_team`(`employee_contract_id`, `team_type`, `team_id`, `status`, `created_by`, `created_on`) VALUES
					('$ecId','$team->team_type','$team->team_id',".Status::PUBLISHED.",'$user_id',NOW());
				";
				dbExecute($SQL);

				$resp['status']=1;
			}
			else
			{
				$et = EmployeeTeam::model()->findByAttributes(array('employee_contract_id'=>$ecId,'team_type'=>$team->team_type,
					'team_id'=>$team->team_id,'status' => Status::PUBLISHED));

				if(!empty($et))
				{
					$et->status=Status::DELETED;
					$et->save();
					$resp['status']=1;
				}
			}
		}

		echo json_encode($resp);
	}

	public function actionGetTeamRowId()
	{
		$select_id= requestParam('selectedID');

		$team=Team::model()->findByAttributes(array('team_id'=>$select_id,'status' => Status::PUBLISHED));
		$resp['status']=0;

		if(!empty($team))
		{
			$resp['status']=1;
			$resp['row_id']=$team->row_id;
		}

		echo json_encode($resp);
	}

	public function actionGetTeamtype()
	{
		$select_id= requestParam('selectedID');
		$resp['status']=0;

		if($select_id!==NULL && strpos($select_id,'type_')!==\FALSE)
		{
			$resp['status']=1;
			$resp['type']=  substr($select_id, 5);
		}

		echo json_encode($resp);
	}

	private function getTeams($type)
	{
		$SQL="
			SELECT
				`team_id` as id,
				`team_name` as value
			FROM team
			WHERE
					`status`=".Status::PUBLISHED."
				AND `team_type`=$type
				";
				if(!App::hasRight('team','use_every_team'))
				{
					$SQL.="AND `owner_user_id`='".userID()."'
					";
				}
		$SQL.="ORDER BY `value`
		";
		$teams = dbFetchAll($SQL);

		return $teams;
	}

	private function getEmployeeTeamSQL($select_id,$approver_filter)
	{
		$AllData=new GetActiveEmployeeAllData("CURDATE()", "1",$approver_filter);
		$table=$AllData->getTableName();

		$SQL = "
			SELECT
				all_data.`employee_contract_id` as id,
				CONCAT(".Employee::getParam('fullname_with_emp_id_ec_id',["all_data","all_data"]).",".App::getSetting("team_employee_details")." ) as value
			FROM $table all_data
			LEFT JOIN `employee_team` et ON
					et.`employee_contract_id`=all_data.`employee_contract_id`
				AND	et.`team_id`='$select_id'
				AND et.`status`=".Status::PUBLISHED."
			LEFT JOIN company_org_group1 cog1 ON
					cog1.`company_org_group_id` = all_data.`company_org_group1_id`
				AND cog1.`status` = ".Status::PUBLISHED."
				AND CURDATE() BETWEEN cog1.`valid_from` AND IFNULL(cog1.`valid_to`,'".App::getSetting("defaultEnd")."')
			LEFT JOIN company_org_group2 cog2 ON
					cog2.`company_org_group_id` = all_data.`company_org_group2_id`
				AND cog2.`status` = ".Status::PUBLISHED."
				AND CURDATE() BETWEEN cog2.`valid_from` AND IFNULL(cog2.`valid_to`,'".App::getSetting("defaultEnd")."')
			LEFT JOIN company_org_group3 cog3 ON
					cog3.`company_org_group_id` = all_data.`company_org_group3_id`
				AND cog3.`status` = ".Status::PUBLISHED."
				AND CURDATE() BETWEEN cog3.`valid_from` AND IFNULL(cog3.`valid_to`,'".App::getSetting("defaultEnd")."')
			LEFT JOIN `workgroup` wg ON
					wg.`workgroup_id`= all_data.`workgroup_id`
				AND wg.`status`=".Status::PUBLISHED."
				AND CURDATE() BETWEEN wg.`valid_from` AND IFNULL(wg.`valid_to`,'".App::getSetting("defaultEnd")."')
			LEFT JOIN `unit` u ON
					u.`unit_id`= all_data.`unit_id`
				AND u.`status`=".Status::PUBLISHED."
				AND CURDATE() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`,'".App::getSetting("defaultEnd")."')
			";
		return $SQL;
	}

}

?>