<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use Yang;

`/yii2-only';


#yii2: done

class TestController extends GridController
{
	public $layout = '//layouts/main';

	public function __construct()
	{
		parent::__construct("test");
		$this->setModelName("User");
		parent::setTitle("Test dhtmlxgrid's eXcells");
		parent::setRights(/*add*/false,/*mod*/false,/*imod*/false,/*del*/false,/*exp*/false,/*$sel*/false,/*$msel*/false,/*$details*/false);

		$userGroupFilter = "";
		$userGroup = App::getUsergroup("user");
		if (!empty($userGroup)) {
			$userGroupFilter .= " AND ".App::getUsersingroup($userGroup);
		}
		parent::selectConditions('( (NOW() BETWEEN `valid_from` AND `valid_to`) OR (`valid_to` IS NULL AND `valid_from` <= NOW()) )'.$userGroupFilter);

		$js = '
			var leftButtonDown = false;

			var lastRow = null;
			var selectedCells = "";

			function mousedownDhxGridCell(e,cell) {
				if (e.which === 1) {
					selectedCells = "";
					$("#maingrid .objbox table tr td").removeClass("clicked");

					leftButtonDown = true;
					lastRow = $(cell).parent().index();
//					console.log("### mouse left button pressed");

					if ($(cell).hasClass("clicked")) {
						$(cell).removeClass("clicked");
					} else {
						$(cell).addClass("clicked");

						currentColumn = $(cell).index();
						selectedCells += "Row: "+lastRow+" - Column: "+currentColumn+"<br/>";
					}
				}
			}

			function mouseupDhxGridCell(e) {
				if (e.which === 1) {
					leftButtonDown = false;
					lastRow = null;
//					console.log("### mouse left button released");

					responsedialog("Cells are now selected",selectedCells);
				}
			}

			function hoverDhxGridCell(cell) {
				currentRow = $(cell).parent().index();

				if (leftButtonDown && lastRow == currentRow && !$(cell).hasClass("clicked")) {
					currentColumn = $(cell).index();

					selectedCells += "Row: "+currentRow+" - Column: "+currentColumn+"<br/>";
					$(cell).addClass("clicked");
				}
			}

			function eXcell_custom(cell){ //the eXcell name is defined here
				if (cell){                 //the default pattern, just copy it
					this.cell = cell;
					this.grid = this.cell.parentNode.grid;
//					this.cell.onclick=function(e){ clickDhxGridCell(cell); }
					this.cell.onmouseover=function(e){ hoverDhxGridCell(cell); }
					this.cell.onmousedown=function(e){ mousedownDhxGridCell(e,cell); }
					this.cell.onmouseup=function(e){ mouseupDhxGridCell(e); }
					eXcell_ed.call(this); //uses methods of the "ed" type
				}
				this.setValue=function(val){
					this.setCValue(\'<div style="height:40px;line-height:40px;" class="">\'+val+\'</div>\',val);
				}
				this.getValue=function(){
					return this.cell.childNodes[0].innerHTML; // gets the value
				}

				//$(".clickable").hover(function () {
					//console.log("hover");
				//});
			}
			eXcell_custom.prototype = new eXcell;
		';

		$this->setMoreJavaScript($js);
	}

	public function columns()
	{
		return array(
			'username'				=> array('col_type'=>'custom'),
			'email'					=> array('col_type'=>'custom'),
			'rolegroup_id'			=> array('col_type'=>'custom'),
			'lang'					=> array('col_type'=>'custom'),
			'note'					=> array('col_type'=>'custom'),
			'valid_from'			=> array('col_type'=>'custom'),
			'valid_to'				=> array('col_type'=>'custom'),
			'status'				=> array('col_type'=>'custom'),
			'password'				=> array('col_type'=>'custom'),
		);
	}
}
?>
