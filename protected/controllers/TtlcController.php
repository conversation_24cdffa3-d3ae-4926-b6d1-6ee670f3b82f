<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Grid2\Grid2Controller;
	use app\models\Employee;
	use Yang;

`/yii2-only';


#yii2: done

class TtlcController extends Grid2Controller
{
	private $games = [
		['id' => 1, 'value' => 'TTLC 1',],
	];

	public function __construct()
	{
		parent::__construct("ttlc");

		parent::enableLAGrid();
	}

	protected function G2BInit() {
		$this->LAGridDB->setModelName("Ttlc");

		parent::setControllerPageTitleId("page_title_ttlc");

		$this->LAGridRights->overrideInitRights("add",				true);
		$this->LAGridRights->overrideInitRights("modify",			true);
		$this->LAGridRights->overrideInitRights("delete",			true);
		
		$this->LAGridRights->overrideInitRights("paging",			false);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);

		$this->LAGridDB->enableSQLMode();

		$defaultEnd	= App::getSetting("defaultEnd");

		$SQL = "
			SELECT
				ttlc.*,
				games.`game_name` AS game_id_grid,
				".Employee::getParam('fullname', 'p1')." AS p1_employee_id_grid,
				".Employee::getParam('fullname', 'p2')." AS p2_employee_id_grid,
				IF(ttlc.`p1_point` > ttlc.`p2_point`, ".Employee::getParam('fullname', 'p1').", IF(ttlc.`p1_point` < ttlc.`p2_point`, ".Employee::getParam('fullname', 'p2').", '')) AS winner,
				points.`wins`,
				points.`points`
			FROM
				`ttlc`
			LEFT JOIN
				`employee` p1 ON
					p1.`employee_id` = ttlc.`p1_employee_id`
					AND CURDATE() BETWEEN p1.`valid_from` AND IFNULL(p1.`valid_to`, '$defaultEnd')
					AND p1.`status` = 2
			LEFT JOIN
				`employee` p2 ON
					p2.`employee_id` = ttlc.`p2_employee_id`
					AND CURDATE() BETWEEN p2.`valid_from` AND IFNULL(p2.`valid_to`, '$defaultEnd')
					AND p2.`status` = 2
			LEFT JOIN
				(
					";
					$i = 0;
					foreach ($this->games as $game) {
						$SQL .= "SELECT '".$game["id"]."' AS game_id, '".$game["value"]."' AS game_name";

						if (count($this->games)-1 !== $i) {
							$SQL .= " UNION ";
						}

						$i++;
					}
					$SQL .= "
				) games ON
					games.`game_id` = ttlc.`game_id`
			LEFT JOIN
				(
					SELECT
						e.`employee_id`,
						ttlc.`game_id`,
						SUM(IF(
							e.`employee_id` = ttlc.`p1_employee_id` AND ttlc.`p1_point` > ttlc.`p2_point`
								OR
							e.`employee_id` = ttlc.`p2_employee_id` AND ttlc.`p2_point` > ttlc.`p1_point`
							,
							1
							,
							0
						)) AS wins,
						SUM(CASE
							WHEN
								e.`employee_id` = ttlc.`p1_employee_id` AND ttlc.`p1_point` > ttlc.`p2_point`
								THEN ttlc.`p1_point`
							WHEN
								e.`employee_id` = ttlc.`p2_employee_id` AND ttlc.`p2_point` > ttlc.`p1_point`
								THEN ttlc.`p2_point`
							ELSE 0
						END) AS points
					FROM
						`employee` e
					LEFT JOIN
						`ttlc` ON
							e.`employee_id` = ttlc.`p1_employee_id` OR e.`employee_id` = ttlc.`p2_employee_id`
					WHERE
						CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '$defaultEnd')
						AND e.`status` = 2
					GROUP BY
						e.`employee_id`,
							ttlc.`game_id`
				) points ON
					points.`game_id` = ttlc.`game_id`
						AND points.`employee_id` = IF(ttlc.`p1_point` > ttlc.`p2_point`, ttlc.`p1_employee_id`, IF(ttlc.`p1_point` < ttlc.`p2_point`, ttlc.`p2_employee_id`, NULL))
				ORDER BY
					points.`points` DESC
		";

		$this->LAGridDB->setSQLSelection($SQL, "row_id");

		parent::G2BInit();
	}

	public function search() {
		return [];
	}

	public function columns()
	{
		return [
			'game_id_grid'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'add' => false, 'edit' => false, 'width' => 150),
			'game_id'				=>	array(
						'export'	=> false,
						'col_type'	=> 'combo',
						'options'	=>	array(
												'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
												'array'	=> $this->games,
											),
						'col_align'	=> 'center',
						'width'		=> 70,
						'grid'		=> false,
			),
			'p1_employee_id_grid'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'add' => false, 'edit' => false, 'width' => 150, 'cssClass' => 'dark_orange'),
			'p1_employee_id'		=> array(
				'export'			=> true,
				'report_width'		=> 30,
				'col_type'			=> 'combo',
				'options'			=>	array(
											'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
											'sql'			=> "SELECT `employee_id` as id,"
																	. " CONCAT(".Employee::getParam('fullname_with_emp_id').",' - ',`valid_from`) AS value"
																	. " FROM `employee`"
																	. " WHERE `status`=2"
																	. " ORDER BY `last_name`, `first_name`",
											'array'					=> array(array("id"=>"","value"=>"")),
										),
				'width'				=> 200,
				'grid'				=> false,
			),
			'p1_point'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => true, 'width' => 100, 'cssClass' => 'dark_orange'),
			'p2_employee_id_grid'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'add' => false, 'edit' => false, 'width' => 150, 'cssClass' => 'dark_bluegrey'),
			'p2_employee_id'		=> array(
				'export'			=> true,
				'report_width'		=> 30,
				'col_type'			=> 'combo',
				'options'			=>	array(
											'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
											'sql'			=> "SELECT `employee_id` as id,"
																	. " CONCAT(".Employee::getParam('fullname_with_emp_id').",' - ',`valid_from`) AS value"
																	. " FROM `employee`"
																	. " WHERE `status`=2"
																	. " ORDER BY `last_name`, `first_name`",
											'array'					=> array(array("id"=>"","value"=>"")),
										),
				'width'				=> 200,
				'grid'				=> false,
			),
			'p2_point'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => true, 'width' => 100, 'cssClass' => 'dark_bluegrey'),
			'winner'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'add' => false, 'edit' => false, 'width' => 100, 'cssClass' => 'dark_green'),
			'wins'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'add' => false, 'edit' => false, 'width' => 100, 'cssClass' => 'dark_green'),
			'points'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'add' => false, 'edit' => false, 'width' => "*"),
		];
	}

	public function attributeLabels() {
		$attributeLabels = [
			"game_id_grid" => "Játék",
			"game_id" => "Játék",
			"p1_employee_id_grid" => "Játékos 1",
			"p2_employee_id_grid" => "Játékos 2",
			"p1_employee_id" => "Játékos 1",
			"p2_employee_id" => "Játékos 2",
			"p1_point" => "Játékos 1 pont",
			"p2_point" => "Játékos 2 pont",
			"winner" => "Nyertes",
			"wins" => "Győzelmek",
			"points" => "Pontok",
		];

		return $attributeLabels;
	}
}
?>
