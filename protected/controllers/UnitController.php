<?php

/**
 * Unit controller
 *
 */
class UnitController extends Grid2HistoryController
{
	public $layout = '//layouts/main';
	private $parentTableName;
	private $parentIdName;
	private $parentValueName;
	private $user;
	private $usedGroup;
	private $heatmapGroups;
	private $useCompanyAndPayrollRights;
	private $groupHierarchy;
	private $unitControllerShowId;
	private $specialFrameDaysGroup;
	private $frameBalanceFilterGroup;

	public function __construct()
	{
		parent::__construct("unit");
		$this->user 						= userID();
		$this->usedGroup 					= App::getSetting("heatmapGroup");
		$this->heatmapGroups 				= App::getSetting("heatmapGroups");
		$this->useCompanyAndPayrollRights	= App::getSetting("useCompanyAndPayrollRights");
		$this->unitControllerShowId			= App::getSetting("unitController_show_id");
		$this->groupHierarchy				= App::getSetting('group_hierarchy');
		$this->specialFrameDaysGroup		= App::getSetting('specialFrameDaysGroup');
		$this->frameBalanceFilterGroup		= App::getSetting('frameBalanceFilterGroup');

		$groupHierarchy = explode(';', $this->groupHierarchy);
		$parentIndex = array_search('unit', $groupHierarchy)-1;
		if($parentIndex >= 0) {
			$this->parentTableName = $groupHierarchy[$parentIndex];
			$this->parentIdName = strpos($this->parentTableName, 'company_org_group') === 0 ? 'company_org_group_id' : $this->parentTableName.'_id';
			$this->parentValueName = strpos($this->parentTableName, 'company_org_group') === 0 ? 'company_org_group_name' : $this->parentTableName.'_name';
		}
	}

	protected function G2BInit() {
		$this->LAGridDB->setModelName("Unit");

		parent::setControllerPageTitleId("page_title_unit");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		true);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);

		$filters = Yang::session('unit_filters',[]);

		if (isset($filters["date"]))
		{
			$art = new ApproverRelatedGroup;
			$gargSQL = $art->getApproverReleatedGroupSQL("Unit", "companyMainData", false, "'{date}'", "AND", $filters["date"]);

			$unit = new Unit;
			$unitCriteria = new CDbCriteria();
			$unitCriteria->alias = $unit->tableName();
			$unitCriteria->condition = "
					(`unit`.`company_id` = '{company}' OR '{company}' = 'ALL' OR '{company}' = '')
				AND (`unit`.`payroll_id` = '{payroll}' OR '{payroll}' = 'ALL' OR '{payroll}' = '')
				AND (`unit`.`unit_id` = '{unit}' OR '{unit}' = 'ALL' OR '{unit}' = '')
				AND ('{date}' = '' OR ('{date}' BETWEEN `unit`.`valid_from` AND default_end(`unit`.`valid_to`)))
				AND `unit`.`status` = " . Status::PUBLISHED . "
				{$gargSQL["where"]}";
			$unitCriteria->order = "`unit`.`unit_name`";

			$this->LAGridDB->setModelSelection($unit, $unitCriteria);
		}
		parent::setExportFileName(Dict::getValue("export_file_unit"));

		parent::G2BInit();
	}

	/**
	 * Kereső értékek átadása sessionnek az oszlopok felépítéséhez
	 * @return void
	 */
	public function actionSetInitProperties()
	{
		Yang::setSessionValue('unit_filters', requestParam('searchInput'));

		parent::actionSetInitProperties();
	}

	public function search()
	{
		$art = new ApproverRelatedGroup;
		$gargCompanySQL['where'] = "";
		$gargPayrollSQL['where'] = "";
		if ($this->useCompanyAndPayrollRights) {
			$gargCompanySQL = $art->getApproverReleatedGroupSQL("Company", "companyMainData", false, "'{date}'", "AND", "allDate");
			$gargPayrollSQL = $art->getApproverReleatedGroupSQL("Payroll", "companyMainData", false, "'{date}'", "AND", "allDate");
		}
		$company = new Company();
		$companyCriteria = $company->getColumnGridCriteria($gargCompanySQL['where'], "{date}");

		$payroll = new Payroll;
		$payrollCriteria = $payroll->getColumnGridCriteria($gargPayrollSQL['where'], "{date}");
		if ($this->useCompanyAndPayrollRights) {
			$payrollCriteria->condition .= "
				AND ({$payrollCriteria->alias}.company_id = '{company}' OR {$payrollCriteria->alias}.company_id = 'ALL' OR '{company}' like 'ALL')";
		}

		$gargSQL = $art->getApproverReleatedGroupSQL("Unit", "companyMainData", false, "'{date}'", "AND", "allDate");

		$unit = new Unit;
		$unitCriteria = new CDbCriteria();
		$unitCriteria->alias = $unit->tableName();
		$unitFilterByCompanyIdPayrollId = "";
		if ($this->useCompanyAndPayrollRights) {
			$unitFilterByCompanyIdPayrollId = "
				AND ({$unitCriteria->alias}.company_id = '{company}' OR {$unitCriteria->alias}.company_id = 'ALL' OR '{company}' like 'ALL')
				AND ({$unitCriteria->alias}.payroll_id = '{payroll}' OR {$unitCriteria->alias}.payroll_id = 'ALL' OR '{payroll}' like 'ALL')";
		}
		$unitCriteria->condition = "
				({$unitCriteria->alias}.`unit_name` LIKE '%%{search}%%')
			{$unitFilterByCompanyIdPayrollId}
			AND ('{date}' = '' OR ('{date}' BETWEEN `unit`.`valid_from` AND default_end(`unit`.`valid_to`)))
			AND `unit`.`status` = " . Status::PUBLISHED . "
			{$gargSQL["where"]}";
		$unitCriteria->order = "`unit`.`unit_name`";

		return array(
			'date'	        =>
			[
				'col_type'      => 'ed',
				'dPicker'       => true,
				'width'         => '*',
				'label_text'    => Dict::getValue("valid_from"),
				'default_value' => date('Y-m-d'),
				'onchange'      => ["company","payroll"]
			],
			'company'		=>
			[
				'label_text'=> Dict::getValue("company_id"),
				'col_type'	=> 'combo',
				'options'	=>
				[
					'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'	=> $company,
					'modelSelectionCriteria'=> $companyCriteria,
					'comboId'				=> 'company_id',
					'comboValue'			=> 'company_name',
					'array'	=> (!$this->useCompanyAndPayrollRights)?[["id"=>"ALL","value"=>Dict::getValue("all")]]:"",
				],
				'onchange'  => ["payroll"]
			],
			'payroll'		=>
			[
				'label_text'=> Dict::getValue("payroll_id"),
				'col_type'	=> 'combo',
				'options'	=>
				[
					'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'	=> $payroll,
					'modelSelectionCriteria'=> $payrollCriteria,
					'comboId'				=> 'payroll_id',
					'comboValue'			=> 'payroll_name',
					'array'					=> [["id"=>"ALL","value"=>Dict::getValue("all")]],
				],
				'default_value' => 'ALL',
			],
			'unit'			=>
			[
				'col_type'	=>'auto',
				'width'		=>'*',
				'label_text'=>Dict::getValue("unit_name"),
				'options'	=>
				[
					'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'	=> $unit,
					'modelSelectionCriteria'=> $unitCriteria,
					'comboId'				=> 'unit_id',
					'comboValue'			=> 'unit_name',
				],
			],
			'submit'		=> ['col_type'=>'searchBarReinitGrid', 'width'=>'*', 'label_text'=>''],
		);
	}

	/**
	 * @return array customized grid columns (name=>params)
	 */
	public function columns()
	{
		$filters = Yang::session('unit_filters',[]);
		if (!isset($filters['date'])) { return []; }

		$gargCompanySQL['where'] = "";
		$gargPayrollSQL['where'] = "";
		if ($this->useCompanyAndPayrollRights) {
			$art = new ApproverRelatedGroup;
			$gargCompanySQL = $art->getApproverReleatedGroupSQL("Company", "companyMainData", false, "'{$filters["date"]}'", "AND", $filters["date"]);
			$gargPayrollSQL = $art->getApproverReleatedGroupSQL("Payroll", "companyMainData", false, "'{$filters["date"]}'", "AND", $filters["date"]);
		}
		$company = new Company();
		$companyCriteriaGrid = $company->getColumnGridCriteria($gargCompanySQL['where'], $filters["date"]);
		$companyCriteriaDialog = $company->getColumnDialogCriteria($gargCompanySQL['where'], 'valid_from', 'valid_to');


		$payroll = new Payroll;
		$payrollCriteriaGrid = $payroll->getColumnGridCriteria($gargPayrollSQL['where'], $filters["date"]);
		$payrollCriteriaDialog = $payroll->getColumnDialogCriteria($gargPayrollSQL['where'], 'valid_from', 'valid_to');
		$payrollCriteriaDialog->condition .= " AND (`company_id` = '{company_id}' OR 'ALL' = '{company_id}' OR `company_id` = 'ALL')";

		$id = ($this->unitControllerShowId || $this->user == '6acd9683761b153750db382c1c3694f6')?
				['unit_id'		=> ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'edit'=>false],]:
				[];

		$column = [
			'company_id' =>
			[
				'export'	=> true,
				'grid'		=> true,
				'col_type'	=> 'combo',
				'options'	=>
				[
					'mode'					            => Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'	            => $company,
					'modelSelectionCriteriaGridMode'    => $companyCriteriaGrid,
					'modelSelectionCriteriaDialogMode'  => $companyCriteriaDialog,
					'comboId'				            => 'company_id',
					'comboValue'			            => 'company_name',
					'array'	=> (!$this->useCompanyAndPayrollRights)?[["id"=>"ALL","value"=>Dict::getValue("all")]]:"",
				],
				'width'     => "*",
				'onchange'  => ["payroll_id"],
			],
			'payroll_id' =>
			[
				'export'	=> true,
				'grid'		=> true,
				'col_type'	=> 'combo',
				'options'	=>
				[
					'mode'					            => Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'	            => $payroll,
					'modelSelectionCriteriaGridMode'    => $payrollCriteriaGrid,
					'modelSelectionCriteriaDialogMode'  => $payrollCriteriaDialog,
					'comboId'				            => 'payroll_id',
					'comboValue'			            => 'payroll_name',
					'array'					            => (!$this->useCompanyAndPayrollRights)?[["id" => "ALL", "value" => Dict::getValue("all")]]:"",
				],
				'width'     => "*",
			],
			'unit_name'			=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'width'=>'300'],
			'minimal_group_count' => ['export'=>false, 'grid'=>false ,'window'=>false ,'col_type'=>'ed', 'width'=>'300', 'default_value'=>'0',],
			'note'				=> ['export'=>true ,'grid'=>false,'window'=>true ,'col_type'=>'ed', 'width'=>'300'],
			'valid_from'		=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed','dPicker'=>true,
											'onchange'=>['unit_id', 'company_id', 'payroll_id'], 'align'=>'center', 'width'=>'*'],
			'valid_to'			=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed','dPicker'=>true,
											'onchange'=>['unit_id', 'company_id', 'payroll_id'], 'align'=>'center', 'width'=>'*'],
			'status'			=> ['export'=>false,'grid'=>false,'window'=>false,'col_type'=>'combo',
											'options'=>['comboModel'=>'Status','comboId'=>'row_id','comboValue'=>'name'],
											'align'=>'center', 'width'=>'*'],
			'created_by'		=> ['export'=>false,'grid'=>false,'window'=>false,'col_type'=>'combo',
											'options'=>['comboModel'=>'User','comboId'=>'user_id','comboValue'=>'username'],
											'align'=>'center', 'width'=>'*'],
			'created_on'		=> ['export'=>false,'grid'=>false,'window'=>false,'col_type'=>'ed','dPicker'=>true,
											'align'=>'center', 'width'=>'*'],
		];

		if ($this->usedGroup == 'unit' || strpos( $this->heatmapGroups, 'unit') !== false) {
			$column["minimal_group_count"]['grid'] = true;
			$column["minimal_group_count"]['window'] = true;
			$column["minimal_group_count"]['export'] = true;
		}

		if(!is_null($this->parentTableName) && !empty($this->parentTableName)) {
			$column['parent'] =
			[
				'grid'=>true,'window'=>true ,'col_type'=>'combo', 'width'=>'*',
				'options' =>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "SELECT 
									". $this->parentTableName . "." . $this->parentIdName . " as id,
									". $this->parentTableName . "." . $this->parentValueName . " as value
								FROM
									" . $this->parentTableName . "
								WHERE
								" . $this->parentTableName . ".`status` = " . Status::PUBLISHED . " AND ".$this->parentTableName.".`$this->parentValueName` LIKE '%%{search}%%'
								ORDER BY
									".$this->parentTableName.".`" . $this->parentValueName . "` ASC",
				],
			];
		}
		if($this->specialFrameDaysGroup == 'unit' || $this->frameBalanceFilterGroup == 'unit')
		{
			// fel kell venni az app_lookup értékeket, ha majd használni kell (sinia keretegyenleg kezelés miatt 1558) dreher is használja
			$column['special_frameday_option']	= [
				'grid'		=> true,
				'window'	=> true ,
				'col_type'	=> 'combo',
				'width'		=> '300',
				'options'	=> [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> "SELECT 
										`lookup_value` as id,
										`dict_value` as value
									FROM
										`app_lookup`
									LEFT JOIN `dictionary` ON
											`app_lookup`.`dict_id` 	= `dictionary`.`dict_id`
										AND `dictionary`.`valid`	= 1
										AND `dictionary`.`lang`		= '" . Dict::getLang() . "'
									WHERE
											`app_lookup`.`valid`		= 1
										AND `app_lookup`.`lookup_id`	= 'yes_no'
									ORDER BY
										`dict_value` ASC",
					],
			];
		}

		return Yang::arrayMerge($id,$column);
	}
}
?>
