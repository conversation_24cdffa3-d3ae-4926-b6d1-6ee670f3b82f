<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\components\MyActiveForm;
	use app\controllers\GridController;
	use app\models\AuthController;
	use app\models\flow\WorkflowFormItems;
	use Yang;

`/yii2-only';


#yii2: done

require_once(Yang::getBasePath() . "/../../ext/frontend_modules/_mod_dhtmlxSuite/dhtmlxConnector/codebase/grid_connector.php");
require_once(Yang::getBasePath() . "/../../ext/frontend_modules/_mod_dhtmlxSuite/dhtmlxConnector/codebase/combo_connector.php");
require_once(Yang::getBasePath() . "/../../ext/frontend_modules/_mod_dhtmlxSuite/dhtmlxConnector/codebase/db_phpyii.php");
require_once(Yang::getBasePath() . "/../../ext/frontend_modules/_mod_dhtmlxSuite/dhtmlxConnector/codebase/db_phpyiisql.php");

// Extends GridController
class ViewController extends Controller
{
	const CONNECTION_ERROR = 1;
	const UNDEFINED_CONNECTION = 2;
	
	public $layout = 'main';
	
	// Default rights:
	private $viewRight = false;
	private $selectRight, $multiselectRight = false;
	private $addRight, $modRight, $inlinemodRight, $delRight, $pdfRight, $expRight, $detailsRight = false;
	
	private $modelName;
	private $title = "";
	private $controllerId;
	/**
	 * Enable JavaScript into the view to call private functions to run actions
	 * @var string
	 */
	private $moreJavaScript= false;
	
	private $searchBarOpenOnInit = true;
	
	private $selectConditions = null;

	/**
	 *
	 * @var string mode:  MODEL / SQL
	 */
	private $mode = "MODEL";
	private $conn = null; // Connection if mode != MODEL
	
	private $exportFilename = "view";
	private $exportDate = true;
	private $exportTime = true;
	
	private $pk = null;
	
	private $historyEnabled = false;
	
	private $splitColumn = 0;
	
	private $selectionCriteria = null;
	

	private $identifyColumn = "row_id";
	private $historyColumn = null;
	
	private $editDialogTitleColumnIndexes, $editDialogTitleText = null;
	
	private $defaultHistoryValues = array();
	
	private $viewDataEncoding = "utf8";
	
	private $viewListMode = false;
	
	private $listModelName = null;
	
	private $validationResults = array();

	/**
	 * __construct(): contructor
	 * @param string $id
	 * @param string $module
	 */
	public function __construct($id, $module = null)
	{
		$this->controllerId = $id;
		parent::__construct($id, $module);
		
		$this->viewRight = App::getRight($this->controllerId,"view");
		
		$this->addRight = App::getRight($this->controllerId,"add");
		$this->modRight = App::getRight($this->controllerId,"modify");
		$this->inlinemodRight = App::getRight($this->controllerId,"inline_modify");
		$this->delRight = App::getRight($this->controllerId,"delete");
		$this->pdfRight = App::getRight($this->controllerId,"pdf");
		$this->expRight = App::getRight($this->controllerId,"export");
		$this->selectRight = App::getRight($this->controllerId,"select");
		$this->multiselectRight = App::getRight($this->controllerId,"multi_select");
		$this->detailsRight = App::getRight($this->controllerId,"details");
	}

	/**
	 * setMode(): set mode
	 * @param string $mode	"MODEL" / "SQL"
	 * @param string $pk	primary key
	 * @param string $conn	connection
	 */
	public function setMode($mode, $pk = null, $conn = null)
	{
		$this->mode = $mode;
		$this->pk = $pk;
		$this->conn = $conn;
	}
	
	/**
	 * getConn(): get db connection
	 * @return type $_conn
	 */
	public function getConn()
	{
		if ($this->conn)
		{
			$_conn = $this->conn;
		}
		else
		{
			$_conn = Yii::app()->db;
		}
		return $_conn;
	}

	/**
	 * setModelName(): set model name
	 * @param string $modelName
	 */
	public function setModelName($modelName = false)
	{
		$this->modelName = $modelName;
	}

	/**
	 * setTitle(): set title
	 * @param string $title
	 */
	public function setTitle($title = false)
	{
		$this->title = $title;
	}
	
	/**
	 * setMoreJavaScript(): add additional JavaScript
	 * @param string $moreJavaScript
	 */
	public function setMoreJavaScript($moreJavaScript = false)
	{
		$this->moreJavaScript = $moreJavaScript;
	}

	/**
	 * getModelName(): get model name
	 * @return string
	 */
	public function getModelName()
	{
		return $this->modelName;
	}
	
	/**
	 * getControllerId(): get controller Id
	 * @return string
	 */
	public function getControllerId()
	{
		return $this->controllerId;
	}
	
	/**
	 * setSplitColumn(): set where column split
	 * @param integer $column: = 0
	 */
	public function setSplitColumn($column = 0) {
		$this->splitColumn = $column;
	}
	
	/**
	 * searchBarOpenOnInit(): set searchBar open on initialization
	 */
	public function searchBarOpenOnInit() {
		$this->searchBarOpenOnInit = true;
	}
	
	/**
	 * exportSettings(): set export settings
	 * @param string $exportFilename
	 * @param boolean $exportDate
	 * @param boolean $exportTime
	 */
	public function exportSettings($exportFilename = "view",$exportDate = true,$exportTime = true) {
		$this->exportFilename = $exportFilename;
		$this->exportDate = $exportDate;
		$this->exportTime = $exportTime;
	}
	
	public function getExportFilename() {
		return $this->exportFilename;
	}
	
	public function getExportDate() {
		return $this->exportDate;
	}
	
	public function getExportTime() {
		return $this->exportTime;
	}
	
	/**
	 * getDefaults(): get default values
	 * @return array
	 */
	public function getDefaults()
	{
		return array();
	}
	
	public function getJavascriptFunction()
	{
		return null;
	}
	
	/**
	 * getIdentifyColumn(): get identify column
	 * @return type
	 */
	public function getIdentifyColumn()
	{
		return $this->identifyColumn;
	}
	
	public function getHistoryColumn()
	{
		return empty($this->historyColumn)?$this->identifyColumn:$this->historyColumn;
	}
	
	/**
	 * setEditDialogTitle(): set edit dialog title in multi-tab dialog
	 * @param string $columnIndexes		example "0;1"			wich column(s)
	 * @param string $text				example "{0} ({1})"		replace text with value(s)
	 */
	protected function setEditDialogTitle($columnIndexes,$text)
	{
		$this->editDialogTitleColumnIndexes = $columnIndexes;
		$this->editDialogTitleText = $text;
	}
	
	public function getEditDialogTitleColumnIndexes()
	{
		return $this->editDialogTitleColumnIndexes;
	}
	
	public function getEditDialogTitleText()
	{
		return $this->editDialogTitleText;
	}
	
	/*
	 * setRights(): set right: add (window), mod (window), mod (inline), del, export, selection, multiline selection, details dialog, pdf creation
	 */
	protected function setRights($add = null,$mod = null,$imod = null,$del = null,$exp = null,$sel = null,$msel = null,$details = null,$pdf = null)
	{
		if ($add != null) $this->addRight = $add;
		if ($mod != null) $this->modRight = $mod;
		if ($imod != null) $this->inlinemodRight = $imod;
		if ($del != null) $this->delRight = $del;
		if ($pdf != null) $this->pdfRight = $pdf;
		if ($exp != null) $this->expRight = $exp;
		if ($sel != null) $this->selectRight = $sel;
		if ($msel != null) $this->multiselectRight = $msel;
		if ($details != null) $this->detailsRight = $details;
	}

	/**
	 * filters(): set filters
	 * @return array action filters
	 */
	public function filters()
	{
		return array(
			'accessControl', // perform access control for CRUD operations
		);
	}

	/**
	 * Specifies the access control rules.
	 * This method is used by the 'accessControl' filter.
	 * @return array access control rules
	 */
	public function accessRules()
	{
		return array(
			array('allow', // allow authenticated users to access all actions
				'users' => array('@'),
			),
			array('allow', // deny all users
				'users' => array('*'),
			),
		);
	}

	/**
	 * actionIndex():
	 */
	public function actionIndex()
	{
		if (Yang::isAjaxRequest())
		{
			$this->layout = "ajax";
		}
		
		$this->pageTitle = Dict::getValue(Yang::appName())." - ".$this->title;
		
		if ($this->viewRight)
		{
			try
			{
				$this->render('/view/index', array(
					"model" => $this->modelName, "controllerid" => $this->controllerId, "title" => $this->title, "searchBarOpenOnInit" => $this->searchBarOpenOnInit, "mode" => $this->mode,
					"selectRight" => $this->selectRight, "multiselectRight" => $this->multiselectRight,
					"addRight" => $this->addRight, "modRight" => $this->modRight, "inlinemodRight" => $this->inlinemodRight, "delRight" => $this->delRight, "expRight" => $this->expRight,
					"detailsRight" => $this->detailsRight, "pdfRight" => $this->pdfRight,
					"moreJavaScript" => $this->moreJavaScript,
					"splitColumn" => $this->splitColumn,
				));
			}
			catch (Exception $ex)
			{
				$this->generateExceptionMessage($ex);
			}
		}
		else
		{
			$this->redirect(array(Yang::getParam('permdeniedUrl')));
		}
	}
	
	/**
	 * actionSearchbar(): 
	 */
	public function actionSearchbar()
	{
		$this->layout = "ajax";
		
		try
		{
			$this->render('/view/searchbar', array("model" => $this->modelName, "controllerid" => $this->controllerId, "title" => $this->title));
		}
		catch (Exception $ex)
		{
			$this->generateExceptionMessage($ex);
		}
	}
	
	/**
	 * actionSearchmodecookie():
	 */
	public function actionSearchmodecookie()
	{
		$this->layout = "empty";
		
		$cn = 'tiptime_'.$this->controllerId.'_'.userID().'_searchbar_isopen';
		Yang::setCookie($cn,new CHttpCookie($cn, (int)$_POST["opened"]));
	}

	/**
	 * actionViewData(): get view data
	 */
	public function actionViewData()
	{
		if ($this->mode === "MODEL")
		{
			$model = $this->selectMethod();

//			$r = $model->findAll();
			
			$grid = new GridConnector($model, "PHPYii");

			$columns = $this->getModelColumns();

			$grid->configure("-", $model->tableSchema->primaryKey, $columns);
			$grid->render();
		}
		else
		{
			$conn = $this->getConn();
			
			if ($conn instanceof CDbConnection) {
				$gc = new GridConnector($conn,"PHPYiiSQL");

				$columns = $this->getModelColumns();

				$SQL = $this->selectMethod();

				$gc->set_encoding($this->gridDataEncoding);
				$gc->render_complex_sql($SQL, $this->pk, $columns);
			} else if ($conn === GridController::UNDEFINED_CONNECTION) {
				$this->layout = "empty";
				header("Content-Type:text/xml");
				
				echo "<?xml version='1.0' encoding='utf-8' ?><rows><row id='1'><cell><![CDATA[UNDEFINED_CONNECTION]]></cell></row></rows>";
			} else if ($conn === GridController::CONNECTION_ERROR) {
				$this->layout = "empty";
				header("Content-Type:text/xml");
				
				echo "<?xml version='1.0' encoding='utf-8' ?><rows><row id='1'><cell><![CDATA[CONNECTION_ERROR]]></cell></row></rows>";
			}
		}
	}
	
	public function setViewDataEncoding($viewDataEncoding = "utf8")
	{
		$this->viewDataEncoding = $viewDataEncoding;
	}
	
	public function actionSimpleGridData($modelName, $columns, $condition, $order)
	{
		$model = new $modelName;
		
		$criteria = new CDbCriteria();
		if (!empty($condition)) {
			$criteria->condition = "$condition";
		}
		if (!empty($order)) {
			$criteria->order = "$order";
		}
		
		$model->setDbCriteria($criteria);
		
		$grid = new GridConnector($model, "PHPYii");
		
		$pk = $model->tableSchema->primaryKey;
		$grid->configure("-", $pk, $columns);
		$grid->render();
	}
	
	public function actionSimpleInsert($modelName)
	{
		$model = new $modelName;
		
		if (isset($_POST["data"])) {
			$model->attributes = $_POST["data"];
		}
		
		$user_id = userID();
		$model->created_by = $user_id?$user_id:"visitor";
		$model->created_on = date("Y-m-d H:i:s");
		
//		$error = MyActiveForm::_validate($model);
		
		$model->save();
	}
	
	public function actionSimpleDelete($modelName, $rowId)
	{
		$model = $modelName::model()->findByPk($rowId);

		$model->delete();
	}

	/**
	 * actionGridDataEdit(): save grid data
	 */
	public function actionGridDataEdit()
	{
		$this->layout = "empty";
		header("Content-Type:text/xml");

		$selectedId = $_POST["ids"];

		$modelName = $this->modelName;
		$model = new $modelName;

		$modelData = array();

		$i = 0;
		$columns = $this->getModelColumns(true);
		
		foreach ($columns as $column)
		{
			$modelData[$column] = $_POST[$selectedId . "_c" . $i];
			$i++;
		}

		$this->actionSaveToDb($modelData, $selectedId, true);
	}
	
	protected function selectionCriteria($criteria = null)
	{
		$this->selectionCriteria = $criteria;
	}

	private function selectMethod()
	{
		if ($this->mode === "MODEL")
		{
			$modelName = $this->modelName;

			$model = new $modelName;
			
			if ($this->selectionCriteria)
			{
				$criteria = $this->selectionCriteria;
			}
			else
			{
				$gridColumns = $this->getColumns();

				$select = "`row_id`";
				$si = 0;

				foreach ($gridColumns as $column => $options)
				{
					if (!isset($options["listModel"]) || !$options["listModel"]) {
						if (isset($options["col_type"]) && $options["col_type"] === "grid")
						{

						}
						else if (isset($options["col_type"]) && $options["col_type"] === "label")
						{

						}
						else if (isset($options["col_type"]) && $options["col_type"] === "cbuttons")
						{

						}
						else if (isset($options["col_type"]) && $options["col_type"] === "pw")
						{
							$select .= ", '*****' AS `$column`";
						}
						else
						{
							$select .= ", `$column`";
						}
						$si++;
					}
				}
				
				$criteria = new CDbCriteria;

				$criteria->select = $select;
			}
			
			if ($this->selectConditions)
			{
				$keys = array();
				$values = array();
				$i = 0;

				if (isset($_POST)) {
					foreach ($_POST as $modelName_ => $value) {
						if (is_array($value)) {
							foreach ($value as $id => $val) {
								$name = $modelName_."_".$id;
								$keys[$i] = "{".$name."}";
								$values[$i] = $val;
								$i++;
							}
						}
					}
				}
				
				$criteria->condition = str_replace($keys,$values,$this->selectConditions);
			}
			$model->setDbCriteria($criteria);

			return $model;
		}
		else
		{
			$keys = array();
			$values = array();
			$i = 0;
		
			if (isset($_POST)) {
				foreach ($_POST as $modelName_ => $value) {
					if (is_array($value)) {
						foreach ($value as $id => $val) {
							$name = $modelName_."_".$id;
							$keys[$i] = "{".$name."}";
							$values[$i] = $val;
							$i++;
						}
					}
				}
			}

			return str_replace($keys,$values,$this->selectConditions);
		}
	}
	
	public function selectConditions($selectConditions = null)
	{
		$this->selectConditions = $selectConditions;
	}


	/**
	 * LEÍRÁS:
	 * Felkészíti az adatbázisba történő feltöltésre a kapott adatokat.
	 * Különböző (modell szinten nem megvalósítható) validációkat végez, jelszót
	 * titkosít, alternatív oszlopok értékeit állítja be.
	 * 
	 * HASZNÁLAT:
	 * Dialogból történő feltöltéskor.
	 */
	private function insertMethod($modelName, $modelData, $saveOnly, $validateOnly)
	{
		$model = new $modelName;
		$gridColumns = $this->getColumns();
		
		$preerrors = array();
		
		foreach ($modelData as $field => $value)
		{
			// pw típusnál sha512-vel mentünk!
			if (isset($gridColumns[$field]['col_type']) && $gridColumns[$field]['col_type'] === "pw")
			{
				// sha512 value
				if (!empty($value) || !empty($modelData[$field.'_re']))
				{
					$password_settings = App::getSetting("passwordStrength");
					$minLength = $password_settings[count($password_settings)-1]['minLength'];
					$match = $password_settings[count($password_settings)-1]['match'];
					
					if (strlen($modelData[$field]) < $minLength)
					{
						$model->addGenericError("error_password_minlength",array("minLength"=>$minLength));
					}
					
					$reqexp_valid = true;
					foreach ($match as $m) {
						if (!preg_match("/$m/", $modelData[$field]))
						{
							$reqexp_valid = false;
						}
					}
					if (!$reqexp_valid)
					{
						$model->addGenericError("error_password_security_conditions");
					}
					
					if ($modelData[$field] !== $modelData[$field.'_re'])
					{
						$model->addGenericError("error_password_doesnt_match");
					}
					
					$modelData[$field] = hash('sha512', $value);
				}
				// feltöltéskor mindig van password_date, hogy üres jelszónál ne validáljon...
				$modelData[$field.'_date'] = date("Y-m-d H:i:s");
			}
		}

		$model->attributes = $modelData;

		$user_id = userID();
		$model->created_by = $user_id?$user_id:"visitor";
		$model->created_on = date("Y-m-d H:i:s");

		$this->validateAndSaveModel($modelName, $model, null, null, false, $preerrors, $saveOnly, $validateOnly);
	}

	/**
	 * LEÍRÁS:
	 * Felkészíti az adatbázisba történő szerkesztésre a kapott adatokat.
	 * Különböző (modell szinten nem megvalósítható) validációkat végez, jelszót
	 * titkosít, alternatív oszlopok értékeit állítja be.
	 * 
	 * HASZNÁLAT:
	 * Dialogból történő szerkesztéskor.
	 */
	private function updateMethod($modelName, $modelData, $edit, $newHistoryItem, $xmlResponse, $saveOnly, $validateOnly)
	{
		$model = $modelName::model()->findByPk($edit);
		$gridColumns = $this->getColumns();
		
		$preerrors = array();
		
		foreach ($modelData as $field => $value)
		{
			// pw típusnál sha512-vel mentünk!
			if (isset($gridColumns[$field]['col_type']) && $gridColumns[$field]['col_type'] === "pw")
			{
				$msg = '';
					
				if (isset($modelData[$field.'_prev']) && empty($modelData[$field.'_prev']))
				{
					$msg .= Dict::getValue("error_prev_password_required").'<br/>';
				}

				// sha512 value
				if ( !empty($value) || !empty($modelData[$field.'_re']) || isset($modelData[$field.'_prev']))
				{
					$hash_value = hash('sha512', $value);
					
					if (isset($modelData[$field.'_prev']) && !empty($modelData[$field.'_prev']))
					{
						$prev_password = hash('sha512', $modelData[$field.'_prev']);

						if ($model->password !== $prev_password)
						{
							$model->addGenericError("error_prev_password_incorrect");
						}
						
						// ha az új jelszó = a régivel
						if ($hash_value === $prev_password)
						{
							$model->addGenericError("error_old_new_password_must_be_different");
						}
					}

					$password_settings = App::getSetting("passwordStrength");
					$minLength = $password_settings[count($password_settings)-1]['minLength'];
					$match = $password_settings[count($password_settings)-1]['match'];

					if (strlen($modelData[$field]) < $minLength)
					{
						$model->addGenericError("error_password_minlength",array("minLength"=>$minLength));
					}

					$reqexp_valid = true;
					foreach ($match as $m) {
						if (!preg_match("/$m/", $modelData[$field]))
						{
							$reqexp_valid = false;
						}
					}
					if (!$reqexp_valid)
					{
						$model->addGenericError("error_password_security_conditions");
					}

					if ($modelData[$field] !== $modelData[$field.'_re'])
					{
						$model->addGenericError("error_password_doesnt_match");
					}

					$modelData[$field] = $hash_value;

					// az új jelszó !== a régivel, akkor változik a password_date
					if ($modelData[$field] !== $model->$field)
					{
						$modelData[$field.'_date'] = date("Y-m-d H:i:s");
					}
				}
				// dont change
				else
				{
					$modelData[$field] = $model->$field;
				}
			}
			unset($modelData[$field.'_re']);
		}

		$model->attributes = $modelData;
		
		$this->validateAndSaveModel($modelName, $model, $edit, $newHistoryItem, $xmlResponse, $preerrors, $saveOnly, $validateOnly);
	}

	private function getModelColumns($arrayReturn = false)
	{
		if ($this->mode === "MODEL")
		{
			$modelName = $this->modelName;
			$model = new $modelName;
		}
			
		$gridColumns = $this->getColumns();
		$columnsArray = array();
		$i = 0;
		
		foreach ($gridColumns as $selectedColumnId => $selectedColumnValue) {
			if (isset($selectedColumnValue["grid"]) && $selectedColumnValue["grid"] == true && (!isset($selectedColumnValue["listModel"]) || !$selectedColumnValue["listModel"]) && $selectedColumnValue["col_type"] !== "label" && $selectedColumnValue["col_type"] !== "cbuttons")
			{
				$columnsArray[$i] = $selectedColumnId;
				$i++;
			}
		}
		
		if ($this->subgridEnabled)
		{
			if ($this->mode === "MODEL") // WHEN model mode, we can use its primary key
			{
				$columnsArray_tmp = array();
				$columnsArray_tmp[0] = $model->tableSchema->primaryKey;
				
				for ($i = 0; $i < count($columnsArray); $i++)
				{
					$j = $i+1;
					$columnsArray_tmp[$j] = $columnsArray[$i];
				}
				
				$columnsArray = $columnsArray_tmp;
			}
		}

		if (!$arrayReturn)
		{
			return implode(",", $columnsArray);
		}
		else
		{
			return $columnsArray;
		}
	}

	public function gridFilters()
	{
		return false;
	}
	
	/**
	 * actionExport(): Make export like $exportType parameter XLS or PDF
	 *		@see PHPExcel
	 * Parameters:
	 *		$this->modelName
	 *		$this->getColumns()
	 *		$model->attributeLabels()
	 *		$this->selectMethod()
	 *		$this->exportFilename
	 * 
	 * echo json_encode($response)
	 * 
	 * @param string $exportType	Exporttype: XLS / PDF
	 * @throws CHttpException
	 */
	public function actionExport($exportType= 'XLS')
	{

		set_time_limit(300);
		
		//**$exportType= 'XLS';
		//**$exportType= 'PDF';
		
		if ($this->mode === "MODEL")
		{
			$modelName = $this->modelName;
			$model = new $modelName;
			$gridColumns = $this->getColumns();
			$attributeLabels = $model->attributeLabels();
			
			$model = $this->selectMethod();
			$res = $model->findAll(); //count($results) > 0
			
			$results = array();
			if (sizeof($res)){
				$name = get_class($this);
				$i = 0;
				foreach ($res as $obj) {
					$results[$i]=$obj->getAttributes();
					$v = get_object_vars($obj);
					foreach ($v as $key => $value) {
						$results[$i][$key] = $value; 
					}
					$i++;
				}
			}
		}
		else
		{
			$gridColumns = $this->getColumns();
			$attributeLabels = $this->attributeLabels();
			
			$SQL = $this->selectMethod();
			$results = $this->getConn()->createCommand($SQL)->queryAll();
		}

		$letters = array("A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z");
		$rowid = 1;
		$letters_count = count($letters);

		$objPHPExcel = new PHPExcel();

		$objPHPExcel->setActiveSheetIndex(0);
		
		switch ($exportType)
		{
			case 'PDF':
				// Set fontsize
				$objPHPExcel->getDefaultStyle()->getFont()
						//->setName('times')
						->setSize(8);
				break;
			
			case 'XLS':
			default:

				break;

		}

		// --> generate header
		$colNo = 0;
		foreach ($gridColumns as $selectedColumnId => $selectedColumnValue)
		{
			//** $selectedColumnValue['export']= true;
			if (isset($selectedColumnValue['export']) && $selectedColumnValue['export']===true)
			{
				foreach ($attributeLabels as $modelColumnId => $modelColumnTitle)
				{
					if ($modelColumnId === $selectedColumnId)
					{
						$current_letter = $letters[$colNo%$letters_count];
						$objPHPExcel->getActiveSheet()->SetCellValue($current_letter.$rowid, $modelColumnTitle);
						// Bold
						$objPHPExcel->getActiveSheet()->getStyle($current_letter.$rowid)->getFont()->setBold(true);
						$objPHPExcel->getActiveSheet()->getColumnDimension($current_letter)->setAutoSize(true);
						$colNo++;
					}
				}	
			}
		}
		$rowid++;
		// <-- generate header
		
		// No column, empty report
		if (!$colNo)
		{
			$response = array(
				'success' => false,
				//'url' => baseURL().'/reports/'.$filename,
				'error' => __FUNCTION__.':'.__LINE__.': Empty report, set export fields in controller columns() function!'
			);

			header('Content-type: application/json');

			echo json_encode($response);
			exit;
		}
		// Store in memory the loaded values...
		$dropdown_arrays= array();
		
		foreach ($results as $row) {
			$colNo = 0;
			foreach ($gridColumns as $selectedColumnId => $selectedColumnValue) {
				//** $selectedColumnValue['export']= true;
				if (isset($selectedColumnValue['export']) && $selectedColumnValue['export']===true)
				{
					foreach ($row as $column => $value) {
						if ($column === $selectedColumnId)
						{
							$new_value= $value;
							$current_letter = $letters[$colNo%$letters_count];
							
							$_modelName= '';
							if (isset($selectedColumnValue['options']['comboModel']))
							{
								$_modelName= $selectedColumnValue['options']['comboModel'];
							}
							
							// 
							if (isset($selectedColumnValue['options']['comboModel'])
									&& $selectedColumnValue['options']['comboModel']=='SQL')
							{
								//print(__LINE__.': options.comboModel == SQL');
								$_modelName= "SQL";
							}
							
							// combo: array -> key -> id, value
							if (($selectedColumnValue['col_type']=='combo')
								&& (isset($selectedColumnValue['array'])))
							{
								// In memory?
								if (isset($dropdown_arrays[$selectedColumnId]))
								{
									$result_dropdown= $dropdown_arrays[$selectedColumnId];
								}
								// Not in memory...
								else
								{
									$result_dropdown= $this->actionDropdown(/* $column */ $selectedColumnId, /* $_modelName */ $_modelName, /* $_id */ 1, /* $_value */ 2,
																	/* $type */ 'ARRAY');
									// Save into memory...
									$dropdown_arrays[$selectedColumnId] = $result_dropdown;
								}
								// Get values...
								foreach ($result_dropdown as $key => $array)
								{
									if (isset($array['id']) && ($array['id']==$value))
									{
										$new_value= $array['value'];
									}
								}
							}
							// combo: SQL: array -> key -> id, value
							else if (($selectedColumnValue['col_type']=='combo')
								&& (isset($selectedColumnValue['options'])))
							{
								// In memory?
								if (isset($dropdown_arrays[$selectedColumnId]))
								{
									$result_dropdown= $dropdown_arrays[$selectedColumnId];
								}
								else
								{
									$result_dropdown= $this->actionDropdown(/* $column */ $selectedColumnId, /* $_modelName */ $_modelName,
															/* $_id */ $selectedColumnValue['options']['comboId'], /* $_value */ $selectedColumnValue['options']['comboValue'],
															/* $type */ 'ARRAY');
									$dropdown_arrays[$selectedColumnId] = $result_dropdown;
								}
								
								foreach ($result_dropdown as $key => $array)
								{
									if (isset($array['id']) && ($array['id']==$value))
									{
										$new_value= $array['value'];
									}
								}
							}
							$objPHPExcel->getActiveSheet()->SetCellValue($current_letter.$rowid, $new_value);
							$colNo++;
						}
					}
				}
			}
			$rowid++;
		}

		// Add controller title to sheet title...
		$title= 'TTWA Report';
		$sheet_title= $title;
		if (isset($this->title))
		{
			$title.= ' - '.$this->title;
			// Sheetname max. 31 character length
			if (strlen($sheet_title.' - '.$this->title)<=31)
			{
				$sheet_title.= ' - '.$this->title;
			}
		}
		// Some of the printable ASCII characters are invalid:  * : / \ ? [ ]
		$sheet_title= strtr($sheet_title, '*:/\\?[]', '_______');
				
		$objPHPExcel->getActiveSheet()->setTitle($sheet_title);
		//  Document info
		$objPHPExcel->getProperties()->setTitle($title)
									->setSubject($title)
									->setDescription($title)
									->setKeywords($title)
									->SetCreator($title)
									->setLastModifiedBy($title);
		// Set PageMargins:
		$objPHPExcel->getActiveSheet()->getPageMargins()->setLeft(0.3);
		$objPHPExcel->getActiveSheet()->getPageMargins()->setRight(0.3);
		$objPHPExcel->getActiveSheet()->getPageMargins()->setTop(0.3+0.2);	// + due to Header
		$objPHPExcel->getActiveSheet()->getPageMargins()->setBottom(0.3);
		
		// - Common:
			$objPHPExcel->getActiveSheet()->getPageSetup()->setOrientation(PHPExcel_Worksheet_PageSetup::ORIENTATION_LANDSCAPE);
			$objPHPExcel->getActiveSheet()->getPageSetup()->setPaperSize(PHPExcel_Worksheet_PageSetup::PAPERSIZE_A4);
		
			// Header:
			// - 
			$objPHPExcel->getActiveSheet()
					->getHeaderFooter()->setOddHeader('&C&B'.$title);
			// - Add a drawing to the header:
			$objDrawing = new PHPExcel_Worksheet_HeaderFooterDrawing();
			$objDrawing->setName('Login logo');
			$objDrawing->setPath('../webroot/images/login-logo.jpg');
			$objDrawing->setHeight(36);
			$objPHPExcel->getActiveSheet()->getHeaderFooter()->addImage($objDrawing, PHPExcel_Worksheet_HeaderFooter::IMAGE_HEADER_LEFT);
			
			// Footer:
			$objPHPExcel->getActiveSheet()
					// 1. Fájlnév - Munkalap
					// 2. Akt. oldal / Összes oldal
					// 3. Dátum időpont
					->getHeaderFooter()->setOddFooter('&L&F - &A &C&P. oldal / &N &R&D &T');
//				$objPHPExcel->getActiveSheet()
//						->getHeaderFooter()->setEvenFooter('&L&B &C&P. oldal / &N &R&F');

				
		switch ($exportType)
		{
			case 'PDF':
				// Change these values to select the Rendering library that you wish to use
				// and its directory location on your server
				//$rendererName = PHPExcel_Settings::PDF_RENDERER_TCPDF;
				$rendererName = PHPExcel_Settings::PDF_RENDERER_TCPDF;
				//$rendererName = PHPExcel_Settings::PDF_RENDERER_DOMPDF;
				$rendererLibrary = 'tcPDF';
				//$rendererLibrary = 'mPDF5.4';
				//$rendererLibrary = 'domPDF0.6.0beta3';
				$rendererLibraryPath = dirname(__FILE__).'/../../../libraries/PDF/' . $rendererLibrary;
				$rendererLibraryPath = dirname(__FILE__).'/../extensions/' . $rendererLibrary;
				if (!PHPExcel_Settings::setPdfRenderer(
					$rendererName,
					$rendererLibraryPath
					)) {
					die(
					'NOTICE: Please set the $rendererName and $rendererLibraryPath values' .
					'<br />' .
					'at the top of this script as appropriate for your directory structure'
					);
					throw new CHttpException(500,'PDF error in '.__FUNCTION__.'() at line number'
							.__LINE__);
				}
				$objPHPExcel->setActiveSheetIndex(0);
				$objPHPExcel->getActiveSheet()->getPageSetup()->setFitToPage(true);
				$objPHPExcel->getActiveSheet()->getPageSetup()->setFitToWidth(true);
				$objPHPExcel->getActiveSheet()->getPageSetup()->setFitToHeight(false);
				
				$objPHPExcel->getActiveSheet()->getPageSetup()->setScale(50);
				
				$objPHPExcel->getActiveSheet()->getPageSetup()->setHorizontalCentered(true);
				//$objPHPExcel->getActiveSheet()->getPageSetup()->setHorizontal(true);
				$objPHPExcel->getActiveSheet()->getPageSetup()->setVerticalCentered(true);
				//$objPHPExcel->getActiveSheet()->getPageSetup()->setVertical(true);
				//$objPHPExcel->getActiveSheet()->getPageSetup()->setTextRotation(true);
				//$objPHPExcel->getActiveSheet()->getPageSetup()->setWrapText(true);
				//$objPHPExcel->getActiveSheet()->getPageSetup()->setShrinkToFit(true);
				//$objPHPExcel->getActiveSheet()->getPageSetup()->setIndent(true);
				
				// In PDF there was border without it! :-)
				$objPHPExcel->getActiveSheet()->setShowGridlines(false);
				
				// Set borders, HTML default none -> PDF thick line!!!
				//	BORDER_NONE -> none
				//	BORDER_THIN, BORDER_DASHDOT, BORDER_DASHDOTDOT, BORDER_DASHED,  -> thick!! :-(
				//	 -> thin :-)
				$border= PHPExcel_Style_Border::BORDER_NONE;
				$objPHPExcel->getDefaultStyle()->getBorders()
						->getTop()->setBorderStyle($border);
				$objPHPExcel->getDefaultStyle()->getBorders()
						->getBottom()->setBorderStyle($border);
				$objPHPExcel->getDefaultStyle()->getBorders()
						->getLeft()->setBorderStyle($border);
				$objPHPExcel->getDefaultStyle()->getBorders()
						->getRight()->setBorderStyle($border);
				
				ini_set('ERROR_LEVEL', E_ALL);
				// Filename?  _YYYY.MM.DD.pdf
				$filename = $this->exportFilename.($this->exportDate?'_'.date("Y.m.d"):'').($this->exportTime?"_".date("H.i.s"):'').'.pdf';
				$objWriter = new PHPExcel_Writer_PDF_tcPDF2($objPHPExcel);
				$objWriter->setPaperSize(PHPExcel_Worksheet_PageSetup::PAPERSIZE_A4);
				$objWriter->setFont('times');
				$objWriter->setOrientation(PHPExcel_Worksheet_PageSetup::ORIENTATION_LANDSCAPE);
				//	$log= __FILE__.':'.__LINE__.'>'.$objWriter->getUseInlineCss()."\n";
				//	error_log($log,3,'error.log');
				$objWriter->setUseInlineCSS(false);
				// CSS készítése:
				//**print($objWriter->generateStyles(/* $generateSurroundingHTML */ true));
				$objWriter->setGenerateSheetNavigationBlock(true);
				
				//$objWriter->writeAllSheets(); 
				break;
			
			case 'XLS':
			default:
				// Filename?  _YYYY.MM.DD.xlsx
				$filename = $this->exportFilename.($this->exportDate?'_'.date("Y.m.d"):'').($this->exportTime?"_".date("H.i.s"):'').'.xlsx';
				$objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
				break;

		}

		$bp = Yang::getBasePath();
		
		$objWriter->save($bp.'/../webroot/reports/'.$filename);
		
		$url= baseURL().'/reports/'.$filename;
		$response = array(
			'success' => true,
			'url' => baseURL().'/reports/'.$filename,
			'error' => PHPExcel_Settings::getPdfRendererPath()
		);

		header('Content-type: application/json');

		echo json_encode($response);
	}
	
	public function actionPDF()
	{
		$this->actionExport('PDF');
	}
	
	protected function generateExceptionMessage($ex)
	{
		$application_log= '';
			$application_log.= __FILE__.':Exception:'."\n";
		$user = Yang::getUserFullName();
			$application_log.= '- User: '.$user."\n";
		$datetime = date("Y-m-d H:i:s");
			$application_log.= '- Time: '.$datetime."\n";
		$contr = $this->controllerId;
			$application_log.= '- Controller: '.$contr."\n";
		//echo '>'.$ex->getMessage().'<';
		$msg = $ex->getMessage();
			$application_log.= '- Message: '.$msg."\n";
		// SQL utasítások miatt, ahol nincs vége az idézőjelnek.
		$msg = addslashes($msg);
		$msg = substr($msg, 0, strpos($msg, "'")-1);
		$msg = substr($msg, 0, strpos($msg, '"')-1);
		$msg = str_replace(array('"', "'", '\\'),array("&quot;","'", '\\\\'),$msg);
		$msg= 'For more information see the application log! '.$msg;
		$file = str_replace(array('"', "'", '\\'),array("&quot;","'", '\\\\'),$ex->getFile());
			$application_log.= '- File: '.$file."\n";
		//$file = '';
		$line = str_replace(array('"', "'", '\\'),array("&quot;","'", '\\\\'),$ex->getLine());
			$application_log.= '- Line: '.$line."\n";
		//$line = '';
		$trace = str_replace(array('"', "'","\n","#", '\\'),array("&quot;","'","<br/>","\#", '\\\\'),$ex->getTraceAsString());
			$application_log.= '- Trace: '."\n";
			$application_log.= $trace."\n";
		$application_log.= '--------------------'."\n";
		$exceptiondialog = '<script type="text/javascript"> exceptiondialog("'."User: <b>".$user."</b><br/>"."Controller: <b>".$contr."</b><br/>"."Date: <b>".$datetime."</b><br/>Message: <b>".$msg."</b><br/>File: <b>".$file." - ".$line."</b><br/>Trace: <b>".$trace.'</b>"); </script>';
		echo $exceptiondialog;
		error_log($application_log, 3, Yang::getRuntimePath().'/application.log');
	}
	
	public function actionDropdown($column, $_modelName, $_id, $_value, $type = null, $conn = null, $useController = null, $mainColumn = null, $encoding = "utf-8")
	{
//		header('Content-Type: text/html; charset='.$encoding);
		
		$results = array();
		
		$_select_conditions = null;
		
		if ($type === "AMD")
		{
			$gc = $this->getColumns();
			$modelName = $this->modelName;
			if (isset($gc[$column]['options']['selectConditions']))
			{
				$_select_conditions = $gc[$column]['options']['selectConditions'];
//                die(__LINE__.' | '.$_select_conditions);
				foreach ($gc as $col => $value)
				{
					if (isset($_GET["amd_".$modelName."_".$col]))
					{
						$_select_conditions = str_replace("{".$modelName."_".$col."}",$_GET["amd_".$modelName."_".$col],$_select_conditions);
					}
				}
			}
		}
		else if ($type === "SB")
		{
			$gf = $this->gridFilters();
			$modelName = $this->modelName;
			if (isset($gf[$column]["options"]["selectConditions"]))
			{
				$_select_conditions = $gf[$column]["options"]["selectConditions"];
				foreach ($gf as $col => $value)
				{
					if (isset($_GET[$modelName."_".$col]))
					{
						$_select_conditions = str_replace("{".$modelName."_".$col."}",$_GET[$modelName."_".$col],$_select_conditions);
					}
					if (isset($_POST[$value['model']][$value['options']['comboValue']]))
					{
						$_select_conditions = str_replace("{".$value['model']."_".$value['options']['comboValue']."}",
								$_POST[$value['model']][$value['options']['comboValue']],$_select_conditions);
					}
				}
			}
		}
		else if ($type === "WF")
		{
			$workflow_form_items = WorkflowFormItems::model()->findByPk($column);
			$_select_conditions = $workflow_form_items->sql_condition;
			$workflow_form_id = $workflow_form_items->workflow_form_id;
			if (!empty($_select_conditions))
			{
				$workflow_form_items = new WorkflowFormItems;
				$criteria = new CDbCriteria;
				$criteria->condition = "`workflow_form_id` = '$workflow_form_id'";
				$res_workflow_form_items = $workflow_form_items->findAll($criteria);
				foreach ($res_workflow_form_items as $value) {
					if (isset($_GET["Action_form_".$value->item_id]))
					{
						$_select_conditions = str_replace("{Action_form_".$value->item_id."}",$_GET["Action_form_".$value->item_id],$_select_conditions);
					}
				}
			}
		}
		else if ($type === "GRID" || $type === "ARRAY")	//  
		{
			if (!empty($useController)) {
				$c = new $useController;
				$gc = $c->columns();
				if (!empty($mainColumn)) {
					$gc = $gc[$mainColumn];
				}
//				$gc = $gc[$column];
				if ($gc['col_type'] === 'grid') {
					$gc = $gc['columns'];
				}
			} else {
				$gc = $this->getColumns();
			}
			
			if (isset($gc[$column]['options']['selectConditionsGrid']))
			{
				$_select_conditions = $gc[$column]['options']['selectConditionsGrid'];
			}
			else if (isset($gc[$column]['options']['selectConditions']))
			{
				$_select_conditions = $gc[$column]['options']['selectConditions'];
//                die(__LINE__.' | '.$_select_conditions);
			}
		}
		
		$i = 0;
		// array
		if (isset($gc) && isset($gc[$column]['array']))
		{
			$i = count($gc[$column]['array']);
			$results = $gc[$column]['array'];
		}
		// model OR SQL code
		else
		{
			if ($_modelName === "SQL")
			{
				$sql = $_select_conditions;

				if (!empty($conn)) {
					$conn = Yii::app()->$conn; //navisionForPET // #see https://innote.login.hu/n-7wpd5adl
					$res = $conn->createCommand($sql)->queryAll();
				} else {
					$res = $this->getConn()->createCommand($sql)->queryAll();
				}

				$results = array();
				$i = 0;
				for ($i = 0; $i < count($res); $i++)
				{
					$results[$i]['id'] = $res[$i][$_id];
					$results[$i]['value'] = $res[$i][$_value];
				}
			}
			else if ($_modelName === "COL")
			{
				$auth_controller = new AuthController;
				
				$criteria = new CDbCriteria;
				$criteria->select = "`controller_name`, `controller_id`";
				
				// GRID-nél nincs select conditions!!!!
				if ($type !== "GRID")
				{
					$criteria->condition = "1 $_select_conditions";
				}
				
				$res_auth_controller = $auth_controller->findAll($criteria);
				
				$results = array();
				$results[0]['id'] = '';
				$results[0]['value'] = Dict::getValue("all");
				$i = 1;
				
				$columns = array();
				$modelCols = array();
				
				if (count($res_auth_controller))
				{
					foreach ($res_auth_controller as $auth_controller)
					{
						$controller_name = $auth_controller->controller_name;
						$controller_id = $auth_controller->controller_id;

						$controller = $controller_name."Controller";

						// a controller könyvtáron kívüli controllereket be kell importálni a module configjában
						if (class_exists($controller))
						{
							$c = new $controller($controller_id);

							$columns = $c->getColumns();

							$modelName = $c->getModelName();

							$controllerCols = $c::attributeLabels();

							if (count($controllerCols))
							{
								$modelCols = $controllerCols;
							}
							else
							{
								$modelCols = $modelName::attributeLabels();
							}

							foreach ($columns as $column => $val)
							{
								if (isset($modelCols[$column]))
								{
									$results[$i]['id'] = $column;
									$results[$i]['value'] = $modelCols[$column];
									$i++;
								}
							}
						}
					}
				}
			}
			else if ($_modelName === "")
			{
				
			}
			else
			{
				$model = new $_modelName;
				
				$_origi_value = $_value;
				
				if (method_exists($model, "getParam"))
				{
					$param = $model->getParam($_value);

					if (!empty($param)) {
						$_value = $param;
					}
				}

				$criteria = new CDbCriteria();
//				$criteria->select= "$_id, $_value";
				$criteria->select= "*, $_value AS $_origi_value";
				//$_value= explode(';', $_value);
				//$_value= $_value[0];
				$criteria->group = "$_id";
				$criteria->order = "$_value ASC";

				// GRID-nél nincs select conditions!!!!
				if ($type !== "GRID")
				{
					$criteria->condition = "1 ".$_select_conditions;
				}

				// Ügyfél telephely (ClientSite) esetén csak a név helyett megjeleníti a Név (Cím) -et, úgy, hogy keresni is lehet benne és eszerint rakja sorrendbe!
				if ($_modelName==='ClientSite')
				{
					$criteria->select= array("t.*", "CONCAT(client_site_name, ' (', t.city, ',',t.street,' ',t.street_type, ' ',t.house_number,'.)') AS client_site_name");
					$cond_value= "CONCAT(client_site_name, ' (', t.city, ',',t.street,' ',t.street_type, ' ',t.house_number,'.)')";
					$criteria->order = "client_site_name ASC";
					$criteria->condition = "1 ".$_select_conditions;
					$_value= "client_site_name";
				}

				$res = $model->findAll($criteria);

				$results = array();
				$i = 0;
				
				if (count($res) && isset($gc[$column]['options']['arrayMerge']) && is_array($gc[$column]['options']['arrayMerge']))
				{
					foreach ($gc[$column]['options']['arrayMerge'] as $index => $val)
					{
						$results[$i]['id'] = $index;
						$results[$i]['value'] = $val;
						$i++;
					}
				}

				foreach ($res as $r) {
					$results[$i]['id'] = $r[$_id];
					$results[$i]['value'] = $r[$_origi_value];
					$i++;
				}
			}
		}
		
		if ($type === "GRID")
		{
			$combo = new ComboConnector("Array");
			
			if (empty($encoding)) {
				$encoding = 'utf-8';
			}
			$combo->set_encoding($encoding);
			
			$combo->render_array($results,'id','value');
		}
		else if ($type === "ARRAY")
		{
			return $results;
		}
		else
		{
			if (empty($encoding)) {
				$encoding = 'utf-8';
			}
			header('Content-Type: text/html; charset='.$encoding);
			
			for ($j = 0; $j < $i; $j++)
			{
				echo '<option value="'.$results[$j]['id'].'">'.$results[$j]['value'].'</option>';
			}
		}
	}
	
	public function actionAutocomplete($column, $_modelName, $_id, $_value, $term = null, $type = null)
	{
		$results = array();
		
		$_select_conditions = null;
		
		if ($type === "AMD")
		{
			$gc = $this->getColumns();
			$modelName = $this->modelName;
			$_select_conditions = null;
			if (isset($gc[$column]['options']['selectConditions']))
			{
				$_select_conditions = $gc[$column]['options']['selectConditions'];
//                die(__LINE__.' | '.$_select_conditions);
				foreach ($gc as $column => $value)
				{
					if (isset($_GET["amd_".$modelName."_".$column]))
					{
						$_select_conditions = str_replace("{".$modelName."_".$column."}",$_GET["amd_".$modelName."_".$column],$_select_conditions);
					}
				}
			}
		}
		else if ($type === "SB")
		{
			$gf = $this->gridFilters();
			$modelName = $this->modelName;
			if (isset($gf[$column]["options"]["selectConditions"]))
			{
				$_select_conditions = $gf[$column]["options"]["selectConditions"];
				foreach ($gf as $column => $value)
				{
					if (isset($_GET[$modelName."_".$column]))
					{
						$_select_conditions = str_replace("{".$modelName."_".$column."}",$_GET[$modelName."_".$column],$_select_conditions);
					}
					if (isset($_POST[$value['model']][$value['options']['comboValue']]))
					{
						$_select_conditions = str_replace("{".$value['model']."_".$value['options']['comboValue']."}",
								$_POST[$value['model']][$value['options']['comboValue']],$_select_conditions);
					}
					
				}
			}
		}
		else if ($type === "WF")
		{
			$workflow_form_items = WorkflowFormItems::model()->findByPk($column);
			$_select_conditions = $workflow_form_items->sql_condition;
			$workflow_form_id = $workflow_form_items->workflow_form_id;
			if (!empty($_select_conditions))
			{
				$workflow_form_items = new WorkflowFormItems;
				$criteria = new CDbCriteria;
				$criteria->condition = "`workflow_form_id` = '$workflow_form_id'";
				$res_workflow_form_items = $workflow_form_items->findAll($criteria);
				foreach ($res_workflow_form_items as $value) {
					if (isset($_GET["Action_form_".$value->item_id]))
					{
						$_select_conditions = str_replace("{Action_form_".$value->item_id."}",$_GET["Action_form_".$value->item_id],$_select_conditions);
					}
				}
			}
		}
		
		if ($_modelName === "SQL")
		{
			$se = $term==="*"?"":$term;
			
			if ($term === "*" || strlen($term) >= 2)
			{
				$sql = str_replace("{term}", $se, $_select_conditions);
			
				$res = $this->getConn()->createCommand($sql)->queryAll();
			
				$results = array();
				$i = 0;
				for ($i = 0; $i < count($res); $i++)
				{
					$results[$i]['id'] = $res[$i][$_id];
					$results[$i]['value'] = $res[$i][$_value];
				}
			}
		}
		else
		{
			$se = $term==="*"?"":$term;
			
			if ($term === "*" || strlen($term) >= 2)
			{
				$model = new $_modelName;
				
				$_origi_value = $_value;
				
				if (method_exists($model, "getParam"))
				{
					$param = $model->getParam($_value);

					if (!empty($param)) {
						$_value = $param;
					}
				}

				$criteria = new CDbCriteria();
				$criteria->select= "*, $_value AS $_origi_value";
				$criteria->group = "$_id";
				$criteria->order = "$_value ASC";

				if ($term === "*")
				{
					$criteria->condition = "1 ".$_select_conditions;
				}
				else
				{
					$criteria->condition = "$_value LIKE '%%$se%%' ".$_select_conditions;
				}
				
				// Ügyfél telephely (ClientSite) esetén csak a név helyett megjeleníti a Név (Cím) -et, úgy, hogy keresni is lehet benne és eszerint rakja sorrendbe!
				if ($_modelName==='ClientSite')
				{
					$criteria->select= array("t.*", "CONCAT(client_site_name, ' (', t.city, ',',t.street,' ',t.street_type, ' ',t.house_number,'.)') AS client_site_name");
					$cond_value= "CONCAT(client_site_name, ' (', t.city, ',',t.street,' ',t.street_type, ' ',t.house_number,'.)')";
					$criteria->order = "client_site_name ASC";
					$criteria->condition = "$cond_value LIKE '%%$se%%' ".$_select_conditions;
					$_value= "client_site_name";
				}

				$res = $model->findAll($criteria);

				$results = array();
				$i = 0;
				foreach ($res as $r) {
					$results[$i]['id'] = $r[$_id];
					$results[$i]['value'] = $r[$_origi_value];
					$i++;
				}
			}
		}
		
		if (strlen($term) < 2 && /*!count($results) &&*/ $term !== "*")
		{
			$results[0]['id'] = '';
			$results[0]['value'] = 'Legalább két karakter beiírása szükséges!';
		}
		else if (!count($results))
		{
			$results[0]['id'] = '';
			$results[0]['value'] = 'Nincs találat!';
		}
		
		$json = json_encode($results);
		echo $json;
	}
	
	public function columns()
	{
		return array();
	}
	
	public function getColumns()
	{
		$columns = $this->columns();
		
		foreach ($columns as $col => $val)
		{
			if (!isset($columns[$col]["grid"])) {
				$columns[$col]["grid"] = false;
				if (App::getRight($this->controllerId,"column_grid_visibility",$col))
				{
					$columns[$col]["grid"] = true;
				}
			}
			if (!isset($columns[$col]["window"])) {
				$columns[$col]["window"] = false;
				if (App::getRight($this->controllerId,"column_window_visibility",$col))
				{
					$columns[$col]["window"] = true;
				}
			}
			if (!isset($columns[$col]["edit"])) {
				$columns[$col]["edit"] = false;
				if (App::getRight($this->controllerId,"column_edit_visibility",$col) != null)
				{
					$columns[$col]["edit"] = App::getRight($this->controllerId,"column_edit_visibility",$col);
				}
			}
			if (!isset($columns[$col]["details"])) {
				$columns[$col]["details"] = false;
				if (App::getRight($this->controllerId,"column_details_visibility",$col) != null)
				{
					$columns[$col]["details"] = App::getRight($this->controllerId,"column_details_visibility",$col);
				}
			}
		}
		
		return $columns;
	}
	
	public function attributeLabels()
	{
		return array();
	}
	
	public function uniqStbuttons()
	{
		return array();
	}
	
	public function uniqDialbuttons()
	{
		return array();
	}
	
	public function dialogTabs()
	{
		return array();
	}

	public function setFooter()
	{
		return null;
	}
	
	public function setGroupBy()
	{
		return null;
	}
	
	public function defaultHistoryValues($defaultHistoryValues = array())
	{
		$this->defaultHistoryValues = $defaultHistoryValues;
	}
	
	public function getDefaultHistoryValues()
	{
		return $this->defaultHistoryValues;
	}
}

?>
