<?php

declare(strict_types=1);

use Components\Employee\Enum\EmployeeMainDataEnum;
use Components\ProjectManagement\Enum\ProjectManagementTaskEnum;
use Components\ProjectManagement\Enum\ProjectManagementTaskLogEnum;
use Components\ProjectManagement\Provider\DynamicDataWithContinuousDataValidityProvider;
use Components\ProjectManagement\Provider\ProjectManagementActiveEmployeesProvider;
use Components\ProjectManagement\Provider\ProjectManagementBaseTasksByEmployeeIdentifierProvider;
use Components\ProjectManagement\Provider\ProjectManagementEmployeeContractIdsByEmployeeIdWithValidity;
use Components\ProjectManagement\Provider\ProjectManagementProconTaskByEmployeeIdentifierProvider;
use Components\ProjectManagement\Provider\ProjectManagementTaskByIdentifierWithProjectStatusProvider;
use Components\ProjectManagement\Provider\ProjectManagementTasksWithActivitiesByTaskIdentifierProvider;
use Components\Vue\Helper;
use Components\WorkScheduleCore\Enum\WorkScheduleUsedTypeOfDayTypeSourceEnum;
use Components\WorkScheduleCore\Transformer\StringTransformer;

Yang::import('application.components.wfm.TimeCardCalculation.TimeCardCalculation');

Yang::loadComponentNamespaces('ProjectManagement');
Yang::loadComponentNamespaces('WorkScheduleCore');
Yang::loadComponentNamespaces('Employee');
Yang::loadComponentNamespaces('Core');
Yang::loadComponentNamespaces('Vue');

final class ProjectManagementPageController extends Controller
{
    private const FULL_DAY_ABSENCE = 99;
    private const REGS = 'regs';
    private const EMPID = 'identifier';
    private const BALANCE = 'balance';
    private const USED_TYPE_OF_DAYTYPE = 'used_type_of_daytype';
    private const USED_DAYTYPE_NAME = 'used_daytype_name';
    private const RESTDAY_BG_COLOR = '#FFF59D';
    private const DEFAULT_BG_COLOR = '#FFFFFF';
    private const ID = 'id';
    private const VALUE = 'value';
    private const DATE = 'date';
    private const DAY_TYPE = 'dayType';
    private const NAME = 'name';
    private const COLOR = 'color';
    private const IS_ENABLED = 'isEnabled';
    private const IS_ABSENCE = 'isAbsence';
    private const ABSENCE_NAME = 'absenceName';
    private const IS_STANDBY = 'isStandby';
    private const STANDBY_NAME = 'standbyName';
    private const STATUS = 'status';
    private const COMMENTS = 'comments';
    private const DETAILS = 'details';
    private const TASK_IDENTIFIER = 'taskIdentifier';
    private const TASK_NAME = 'taskName';
    private const EMPLOYEE_IDENTIFIER = 'employeeIdentifier';
    private const HAS_INPUT = 'hasInput';
    private const BREAK_TIME_SUM = 'breakTimeSum';
    private const HAS_SUM = 'hasSum';
    private const INTERNAL = 'internal';
    private const EXTERNAL = 'external';
    private const TASK_LIST = 'taskList';
    private const PROJECT_TABLE_COLUMNS = 'projectTableColumns';
    private const TASK_TABLE_COLUMNS = 'taskTableColumns';
    private const ROW_IDENTIFIER = 'rowIdentifier';
    private const ROW_NAME = 'rowName';
    private const PROJECT_TABLE_COLUMN_ROWS = [
        'employee_name' => '',
        'location_name' => 'Location',
        'schedule_type' => 'Schedule type',
        'start_time' => 'Start time',
        'end_time' => 'End time',
        'f_time' => 'F-time',
        'balance' => 'Balance/Correction',
        'difference' => 'Difference',
    ];
    private StringTransformer $stringTransformer;
    private int $statusLocked;

    public function __construct()
    {
        parent::__construct('projectManagement/projectManagementPage');
        $this->stringTransformer = new StringTransformer();
        $this->statusLocked = Status::LOCKED;
    }

    /**
     * @throws Exception
     */
    public function actionInitTableColumns(): void
    {
        if (!$this->checkLogin()) {
            return;
        }
        $employeeIdentifier = requestParam(self::EMPLOYEE_IDENTIFIER);
        $validFrom = $this->stringTransformer->convertStringToDate(requestParam('validFrom'));
        $validTo = $this->stringTransformer->convertStringToDate(requestParam('validTo'));
        $employeeContractId = $this->provideEmployeeContractId($validFrom, $validTo, $employeeIdentifier);
        $ecId = reset($employeeContractId);
        if ($ecId === null) {
            Helper::sendJsonResponse([
                self::TASK_LIST => [],
                self::PROJECT_TABLE_COLUMNS => [],
                self::TASK_TABLE_COLUMNS => [],
            ]);
            return;
        }
        $this->runTimeCardCalculation($ecId, $validFrom, $validTo);
        $projectTableColumnRows = self::PROJECT_TABLE_COLUMN_ROWS;
        $assignedProconTaskIdentifiers = $this->provideAssignedProconTasksByEmployeeIdentifier($employeeIdentifier, $validFrom, $validTo);
        $baseTaskIdentifiers = $this->provideBaseTasksByEmployeeIdentifier($employeeIdentifier, $validFrom, $validTo);
        $finalTasks = array_merge($assignedProconTaskIdentifiers, $baseTaskIdentifiers);
        $taskIdentifiers = array_column($finalTasks, 'task_identifier');
        $tasks = $this->provideTaskByIdentifier($taskIdentifiers, $validFrom, $validTo);
        $taskLogs = $this->provideTaskWithActivities($taskIdentifiers, $validFrom, $validTo);

        $employeeCalcUsedDaytypeRegs = new GetEmployeeCalcUsedDaytypeRegs();
        $employeeUsedDatypeRegsMinMaxRegs = $employeeCalcUsedDaytypeRegs->getEcudMinMaxRegs(
            $this->stringTransformer->convertDateToString($validFrom),
            $this->stringTransformer->convertDateToString($validTo),
            $employeeContractId
        );
        $employeeCalc = $this->getEmployeeCalc($validFrom, $validTo, $employeeContractId);
        $workSchedules = $this->getWorkSchedules($validFrom, $validTo, $employeeContractId);
        $tableColumns = $this->getTableColumnsData($tasks, $taskLogs, $workSchedules, $ecId, $employeeIdentifier, true);
        $projectTableColumns = $this->getProjectTableColumnsData(
            $projectTableColumnRows,
            $workSchedules,
            $ecId,
            $employeeUsedDatypeRegsMinMaxRegs,
            $employeeCalc,
            $employeeIdentifier
        );
        $taskList = array_values(array_map(function ($task) {
            return [
                self::ID => $task[ProjectManagementTaskEnum::TASK_IDENTIFIER],
                self::VALUE => $task[ProjectManagementTaskEnum::TASK_NAME],
            ];
        }, $tasks));
        $result = [
            self::TASK_LIST => $taskList,
            self::PROJECT_TABLE_COLUMNS => $projectTableColumns,
            self::TASK_TABLE_COLUMNS => $tableColumns,
        ];

        Helper::sendJsonResponse($result);
    }

    /**
     * @throws Exception
     */
    public function actionGetContinuousDataValidity(): void
    {
        if (!$this->checkLogin()) {
            return;
        }
        $requestedDay = $this->stringTransformer->convertStringToDate(requestParam('day'));
        $validityId = requestParam('validityIdentifier');
        $continuousDataValidity = $this->provideDynamicDataWithContinousDataValidity($requestedDay, $validityId);
        $validityArray = $this->modelsToArray($continuousDataValidity);
        $firstValidityElement = reset($validityArray);
        if ($firstValidityElement === false) {
            header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
            $response = [
                'translations' => [],
                'errorCode' => 'data-empty',
                'correlationId' => '',
                'additionalInfo' => [
                    'validation' => [],
                ],
                'title' => Dict::getValue('filter') . ' - ' . Dict::getValue('no_data'),
                'detail' => Dict::getValue('filter') . ' - ' . Dict::getValue('no_data'),
                'status' => 400,
                'instance' => '',
                'type' => 'SYSTEM',
            ];

            Helper::sendJsonResponse($response);
        } else {
            $firstValidityElement = [
                'validFrom' => $firstValidityElement['valid_from'],
                'validTo' => $firstValidityElement['valid_to']
            ];
        }

        Helper::sendJsonResponse($firstValidityElement);
    }

    /**
     * @throws Exception
     */
    public function actionGetTableColumns(): void
    {
        if (!$this->checkLogin()) {
            return;
        }
        $employeeIdentifier = requestParam(self::EMPLOYEE_IDENTIFIER);
        $validFrom = $this->stringTransformer->convertStringToDate(requestParam('validFrom'));
        $validTo = $this->stringTransformer->convertStringToDate(requestParam('validTo'));
        $taskIdentifiers = requestParam(self::TASK_IDENTIFIER) ?? [];
        $assignedProconTaskIdentifiers = $this->provideAssignedProconTasksByEmployeeIdentifier($employeeIdentifier, $validFrom, $validTo, $taskIdentifiers, true);
        $baseTaskIdentifiers = $this->provideBaseTasksByEmployeeIdentifier($employeeIdentifier, $validFrom, $validTo, $taskIdentifiers, true);
        $finalTasks = array_merge($assignedProconTaskIdentifiers, $baseTaskIdentifiers);
        $taskIdentifiers = array_column($finalTasks, 'task_identifier');
        $tasks = $this->provideTaskByIdentifier($taskIdentifiers, $validFrom, $validTo);
        $taskLogs = $this->provideTaskWithActivities($taskIdentifiers, $validFrom, $validTo);
        $employeeContractId = $this->provideEmployeeContractId($validFrom, $validTo, $employeeIdentifier);
        $workSchedules = $this->getWorkSchedules($validFrom, $validTo, $employeeContractId);
        $ecId = reset($employeeContractId);
        $tableColumns = $this->getTableColumnsData($tasks, $taskLogs, $workSchedules, $ecId, $employeeIdentifier);
        $result = [
            self::TASK_TABLE_COLUMNS => $tableColumns,
        ];

        Helper::sendJsonResponse($result);
    }

    /**
     * @throws Exception
     */
    public function actionGetEmployeeList(): void
    {
        if (!$this->checkLogin()) {
            return;
        }

        $validFrom = $this->stringTransformer->convertStringToDate(requestParam('validFrom'));
        $validTo = $this->stringTransformer->convertStringToDate(requestParam('validTo'));

        $filters = [
            'interval' => [
                EmployeeMainDataEnum::VALID_FROM => $this->stringTransformer->convertDateToString($validFrom),
                EmployeeMainDataEnum::VALID_TO => $this->stringTransformer->convertDateToString($validTo)
            ],
        ];

        $getActiveEmployees = new ProjectManagementActiveEmployeesProvider();
        $activeEmployees = $getActiveEmployees->provide($filters, 'projectManagement');

        $data = [];
        foreach ($activeEmployees as $activeEmployee) {
            $data[] = [
                self::ID => $activeEmployee['employee_id'],
                self::VALUE => $activeEmployee['value'],
            ];
        }

        Helper::sendJsonResponse($data);
    }

    /**
     * @throws Exception
     */
    public function actionSaveForm(): void
    {
        if (!$this->checkLogin()) {
            return;
        }

        $json = file_get_contents('php://input');
        $jsonData = json_decode($json, true);

        if (empty($jsonData[self::TASK_TABLE_COLUMNS]) && empty($jsonData[self::PROJECT_TABLE_COLUMNS])) {
            $this->sendResponseWithReason(\Dict::getValue('no_changes'));
            return;
        }

        $taskTableColumns = $jsonData[self::TASK_TABLE_COLUMNS] ?? [];
        $dayIdsError = [];

        $transaction = Yang::app()->db->beginTransaction();
        try {
            $dayIdsError = $this->processTaskTableColumns($taskTableColumns);
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollback();
            Yang::log($e->getMessage());
        }

        $dayIdsError = array_merge($this->registrationAndBalanceSave($jsonData), $dayIdsError);
        $this->sendResponse($dayIdsError);
    }

    private function provideEmployeeContractId(DateTime $validFrom, DateTime $validTo, string $employeeIdentifier): array
    {
        $employeeContractIds = new ProjectManagementEmployeeContractIdsByEmployeeIdWithValidity();
        $contractIdModels = $employeeContractIds->provide($validFrom, $validTo, [$employeeIdentifier]);
        $contractIdModel = reset($contractIdModels);

        return [$contractIdModel->employee_contract_id];
    }

    private function provideTaskWithActivities(array $taskIdentifiers, DateTime $validFrom, DateTime $validTo, int $limit = 0): array
    {
        $taskWithActivities = new ProjectManagementTasksWithActivitiesByTaskIdentifierProvider();

        return $taskWithActivities->provide($taskIdentifiers, $validFrom, $validTo, $limit);
    }

    private function provideTaskByIdentifier(array $taskIdentifiers, DateTime $validFrom, DateTime $validTo): array
    {
        $tasksByEmployeeIdentifier = new ProjectManagementTaskByIdentifierWithProjectStatusProvider();
        return $tasksByEmployeeIdentifier->provide($taskIdentifiers, $validFrom, $validTo);
    }

    private function provideAssignedProconTasksByEmployeeIdentifier(string $employeeIdentifier, DateTime $validFrom, DateTime $validTo, array $taskIdentifiers = [], $forceTaskIdentifiers = false): array
    {
        $proconTasksByEmployeeIdentifier = new ProjectManagementProconTaskByEmployeeIdentifierProvider();
        return $proconTasksByEmployeeIdentifier->provide($employeeIdentifier, $validFrom, $validTo, $taskIdentifiers, $forceTaskIdentifiers);
    }

    private function provideBaseTasksByEmployeeIdentifier(string $employeeIdentifier, DateTime $validFrom, DateTime $validTo, array $taskIdentifiers = [], $forceTaskIdentifiers = false): array
    {
        $baseTasksByEmployeeIdentifier = new ProjectManagementBaseTasksByEmployeeIdentifierProvider();
        return $baseTasksByEmployeeIdentifier->provide($employeeIdentifier, $validFrom, $validTo, $taskIdentifiers, $forceTaskIdentifiers);
    }

    private function provideDynamicDataWithContinousDataValidity(DateTime $day, mixed $validityIdentifier)
    {
        $dynamicDataWithContinousDataValidity = new DynamicDataWithContinuousDataValidityProvider();
        return $dynamicDataWithContinousDataValidity->provide($day, $validityIdentifier);
    }

    private function getBackGroundColor(array $attributes): ?string
    {
        return $attributes[self::USED_TYPE_OF_DAYTYPE] == WorkScheduleUsedTypeOfDayTypeSourceEnum::RESTDAY
            ? self::RESTDAY_BG_COLOR
            : ($attributes['state_type_bgcolor'] ?? self::DEFAULT_BG_COLOR);
    }

    private function checkIsEnabled(
        array $attributes,
        array $employeeCalc = [],
        string $ecId = '',
        string $date = ''
    ): bool
    {
        if (isset($employeeCalc[$ecId][$date])) {
            $errorTypes = array_column($employeeCalc[$ecId][$date], 'employee_calc_error_type_id');
            if (array_filter($errorTypes, fn($type) => $type != 0 && $type != 1)) {
                return false;
            }
        }

        if (empty($attributes['used_daytype_id'])) {
            return false;
        }

        if (!empty($attributes['absence_name']) && $attributes['state_type_absence_hour'] === self::FULL_DAY_ABSENCE) {
            return false;
        }

        return $attributes['employee_calc_used_daytype_status'] != $this->statusLocked;
    }

    /**
     * @param DateTime $validFrom
     * @param DateTime $validTo
     * @param array $employeeContractId
     * @return array
     */
    private function getWorkSchedules(DateTime $validFrom, DateTime $validTo, array $employeeContractId): array
    {
        $getEmployeeWorkSchedule = new GetEmployeeWorkSchedule(
            $this->stringTransformer->convertDateToString(clone $validFrom),
            $this->stringTransformer->convertDateToString(clone $validTo),
            $employeeContractId,
            false,
            'projectManagement',
            '1,2,6',
            true
        );
        return $getEmployeeWorkSchedule->get();
    }

    private function getWorktime(array $employeeCalc, string $ecId, string $cellDate, array $typeIds = []): ?string
    {
        $workTimeInSeconds = 0;

        if (isset($employeeCalc[$ecId][$cellDate])) {
            foreach ($employeeCalc[$ecId][$cellDate] as $item) {
                if (isset($item['value']) && isset($item['inside_type_id'])) {
                    if (empty($typeIds) || $this->matchesTypeIds($item['inside_type_id'], $typeIds)) {
                        $workTimeInSeconds += $item['value'];
                    }
                }
            }
        } else {
            return null;
        }

        $absWorkTimeInSeconds = abs($workTimeInSeconds);
        $hours = floor($absWorkTimeInSeconds / 3600);
        $minutes = floor(($absWorkTimeInSeconds % 3600) / 60);
        $sign = $workTimeInSeconds < 0 ? '-' : '';

        return sprintf('%s%02d:%02d', $sign, $hours, $minutes);
    }

    private function matchesTypeIds(string $insideTypeId, array $typeIds): bool
    {
        foreach ($typeIds as $typeId) {
            if (strpos($insideTypeId, $typeId) !== false) {
                return true;
            }
        }
        return false;
    }

    /**
     * @throws Exception
     */
    private function getProjectTableColumnsData(
        array $projectTableColumnRows,
        array $workSchedules,
        string $ecId,
        array $employeeUsedDatypeRegsMinMaxRegs,
        array $employeeCalc,
        string $employeeIdentifier
    ): array
    {
        $projectTableColumns = [];
        foreach ($projectTableColumnRows as $columnId => $columnName) {
            if (!isset($workSchedules[$ecId])) {
                continue;
            }
            $rowName = $columnName;
            $hasInput = false;
            $hasSum = false;
            $details = [];
            foreach ($workSchedules[$ecId] as $day => $attributes) {
                $cellDate = $day;
                $backgroundColor = $this->getBackGroundColor($attributes);
                $isAbsence = $this->isAbsence($attributes);
                $absenceName = $this->getAbsenceName($attributes);
                $isStandby = $this->isStandby($attributes);
                $standbyName = $this->getStandbyName($attributes);
                $dayTypeName = $this->getDayTypeName($attributes);
                $isEnabled = $this->checkIsEnabled($attributes, $employeeCalc, $ecId, $cellDate);
                $breakTime = $this->getBreakTime($attributes);
                $status = $attributes['employee_calc_used_daytype_status'];
                $value = null;
                switch ($columnId) {
                    case 'employee_name':
                        $value = $attributes['fullname'];
                        $rowName = $attributes['fullname_without_emp_id'];
                        break;
                    case 'start_time':
                        $dateTime = $employeeUsedDatypeRegsMinMaxRegs[$ecId][$cellDate] !== null ? reset(
                            $employeeUsedDatypeRegsMinMaxRegs[$ecId][$cellDate]
                        )['minreg'] : null;
                        if ($dateTime !== null) {
                            $dateTimeObj = DateTime::createFromFormat('Y-m-d H:i:s', $dateTime);
                            $value = $dateTimeObj ? $dateTimeObj->format('H:i') : null;
                        }
                        $hasInput = true;
                        break;
                    case 'end_time':
                        $value = null;
                        $hasInput = true;
                        if (isset($employeeUsedDatypeRegsMinMaxRegs[$ecId][$cellDate])) {
                            $regs = reset($employeeUsedDatypeRegsMinMaxRegs[$ecId][$cellDate]);
                            if ($regs['minreg'] !== $regs['maxreg']) {
                                $dateTime = $regs['maxreg'];
                                $dateTimeObj = DateTime::createFromFormat('Y-m-d H:i:s', $dateTime);
                                if ($dateTimeObj !== false) {
                                    $value = $dateTimeObj->format('H:i');
                                }
                            }
                        }
                        break;
                    case 'f_time':
                        $value = !empty($attributes['used_full_work_time']) ? secToHis($attributes['used_full_work_time']) : null;
                        $hasSum = true;
                        $hasInput = true;
                        break;
                    case 'balance':
                        $value = $this->getWorktime($employeeCalc, $ecId, $cellDate, ['bot']);
                        $hasSum = true;
                        $hasInput = true;
                        break;
                    case 'difference':
                        $hasInput = true;
                        break;
                    default:
                        break;
                }

                $details[] = [
                    self::DATE => $cellDate,
                    self::DAY_TYPE => [
                        self::ID => $attributes[self::USED_TYPE_OF_DAYTYPE],
                        self::IS_ABSENCE => $isAbsence,
                        self::ABSENCE_NAME => $absenceName,
                        self::IS_STANDBY => $isStandby,
                        self::STANDBY_NAME => $standbyName,
                        self::NAME => $dayTypeName,
                        self::COLOR => $backgroundColor,
                    ],
                    self::VALUE => $value,
                    self::BREAK_TIME_SUM => $breakTime,
                    self::IS_ENABLED => $isEnabled,
                    self::STATUS => $status,
                ];
            }

            $existingRowIndex = array_search($columnId, array_column($projectTableColumns, self::ROW_IDENTIFIER));
            if ($existingRowIndex === false) {
                $projectTableColumns[] = [
                    self::ROW_IDENTIFIER => $columnId,
                    self::ROW_NAME => $rowName,
                    self::EMPLOYEE_IDENTIFIER => $employeeIdentifier,
                    self::HAS_INPUT => $hasInput,
                    self::HAS_SUM => $hasSum,
                    self::DETAILS => $details,
                ];
            } else {
                $projectTableColumns[$existingRowIndex][self::DETAILS] = $details;
            }
        }

        return $projectTableColumns;
    }

    private function getTableColumnsData(
        array $tasks,
        array $taskLogs,
        array $workSchedules,
        string $ecId,
        string $employeeIdentifier,
        bool $init = false
    ): array
    {
        $tableColumns = [];
        if (!isset($workSchedules[$ecId])) {
            return $tableColumns;
        }
        foreach ($workSchedules[$ecId] as $day => $attributes) {
            $cellDate = $day;
            $backgroundColor = $this->getBackGroundColor($attributes);
            $isAbsence = $this->isAbsence($attributes);
            $absenceName = $this->getAbsenceName($attributes);
            $isStandby = $this->isStandby($attributes);
            $standbyName = $this->getStandbyName($attributes);
            $dayTypeName = $this->getDayTypeName($attributes);
            $breakTime = $this->getBreakTime($attributes);
            $isEnabled = true;
            foreach ($tasks as $task) {
                $taskIdentifier = $task[ProjectManagementTaskEnum::TASK_IDENTIFIER];

                if ($init && !in_array($taskIdentifier, array_column($taskLogs, ProjectManagementTaskEnum::TASK_IDENTIFIER))) {
                    continue;
                }

                $taskName = $task[ProjectManagementTaskEnum::TASK_NAME];
                $existingTaskIndex = array_search($taskIdentifier, array_column($tableColumns, self::TASK_IDENTIFIER));

                if ($existingTaskIndex !== false && in_array(
                        $cellDate,
                        array_column($tableColumns[$existingTaskIndex][self::DETAILS], self::DATE)
                    )) {
                    continue;
                }

                $taskLog = array_filter($taskLogs, function ($log) use ($cellDate, $taskIdentifier) {
                    return $log[ProjectManagementTaskLogEnum::TASK_LOG_DAY] === $cellDate && $log[ProjectManagementTaskLogEnum::TASK_LOG_TASK_IDENTIFIER] === $taskIdentifier;
                });

                $taskLog = reset($taskLog);
                $value = $taskLog ? (DateTime::createFromFormat(
                    'H:i:s',
                    $taskLog[ProjectManagementTaskLogEnum::TASK_LOG_TASK_TIME]
                )->format('H:i') ?: null) : null;
                $taskLogStatus = $taskLog ? $taskLog[ProjectManagementTaskLogEnum::TASK_LOG_STATUS] : null;
                $additionalData = $taskLog ? json_decode(
                    $taskLog[ProjectManagementTaskLogEnum::TASK_LOG_ADDITIONAL_DATA],
                    true
                ) : [];
                $comments = null;
                if (!empty($additionalData)) {
                    if (isset($additionalData[ProjectManagementTaskLogEnum::TASK_LOG_INTERNAL_COMMENT])) {
                        $comments[self::INTERNAL] = $additionalData[ProjectManagementTaskLogEnum::TASK_LOG_INTERNAL_COMMENT];
                    }
                    if (isset($additionalData[ProjectManagementTaskLogEnum::TASK_LOG_EXTERNAL_COMMENT])) {
                        $comments[self::EXTERNAL] = $additionalData[ProjectManagementTaskLogEnum::TASK_LOG_EXTERNAL_COMMENT];
                    }
                }

                $details = [
                    self::DATE => $cellDate,
                    self::DAY_TYPE => [
                        self::ID => $attributes[self::USED_TYPE_OF_DAYTYPE],
                        self::IS_ABSENCE => $isAbsence,
                        self::ABSENCE_NAME => $absenceName,
                        self::IS_STANDBY => $isStandby,
                        self::STANDBY_NAME => $standbyName,
                        self::NAME => $dayTypeName,
                        self::COLOR => $backgroundColor,
                    ],
                    self::VALUE => $value,
                    self::BREAK_TIME_SUM => $breakTime,
                    self::IS_ENABLED => $isEnabled,
                    self::STATUS => $taskLogStatus,
                    self::COMMENTS => $comments,
                ];

                if ($existingTaskIndex !== false) {
                    $tableColumns[$existingTaskIndex][self::DETAILS][] = $details;
                } else {
                    $tableColumns[] = [
                        self::TASK_IDENTIFIER => $taskIdentifier,
                        self::TASK_NAME => $taskName,
                        self::EMPLOYEE_IDENTIFIER => $employeeIdentifier,
                        self::HAS_INPUT => true,
                        self::HAS_SUM => false,
                        self::DETAILS => [$details],
                    ];
                }
            }
        }

        return $tableColumns;
    }

    private function getEmployeeCalc(
        DateTime $validFrom,
        DateTime $validTo,
        array $employeeContractId,
        string $status = '1,2,5,6'
    ): array
    {
        $getEmployeeCalc = new GetEmployeeCalc();
        return $getEmployeeCalc->get(
            $this->stringTransformer->convertDateToString($validFrom),
            $this->stringTransformer->convertDateToString($validTo),
            $employeeContractId,
            $status
        );
    }

    /**
     * @throws Exception
     */
    private function processTaskTableColumns(array $taskTableColumns): array
    {
        $employeeIdentifier = null;
        $ecId = null;
        $workSchedules = [];
        $employeeCalc = [];
        $dayIdsError = [];
        $validFromString = requestParam('validFrom');
        $validToString = requestParam('validTo');
        try {
            $validFrom = $this->stringTransformer->convertStringToDate($validFromString);
        } catch (\Exception $e) {
            $validFrom = $this->stringTransformer->convertStringToDateTime($validFromString, 'Y.m.d.');
        }
        try {
            $validTo = $this->stringTransformer->convertStringToDate($validToString);
        } catch (\Exception $e) {
            $validTo = $this->stringTransformer->convertStringToDateTime($validToString, 'Y.m.d.');
        }

        foreach ($taskTableColumns as $taskTable) {
            foreach ($taskTable['details'] as $detail) {
                if (!array_key_exists('date', $detail)) {
                    continue;
                }

                if (is_null($employeeIdentifier)) {
                    $employeeIdentifier = $taskTable[self::EMPLOYEE_IDENTIFIER];
                }

                if (is_null($ecId)) {
                    $employeeContractId = $this->provideEmployeeContractId($validFrom, $validTo, $employeeIdentifier);
                    $ecId = reset($employeeContractId);
                }

                if (empty($workSchedules)) {
                    $workSchedules = $this->getWorkSchedules($validFrom, $validTo, [$ecId]);
                }

                if (empty($employeeCalc)) {
                    $employeeCalc = $this->getEmployeeCalc($validFrom, $validTo, [$ecId]);
                }

                $isValidTaskLogToSave = $this->validateTaskLog($detail);
                if (!$isValidTaskLogToSave) {
                    $dayIdsError[] = $detail['id'];
                } else {
                    $this->saveTaskLog($taskTable, $detail, $employeeIdentifier);
                }
            }
        }

        return $dayIdsError;
    }

    private function validateTaskLog(array $detail): bool
    {
        if (isset($detail['value'])) {
            [$hours, $minutes] = explode(':', $detail['value']);
            $hours = (int)$hours;
            $minutes = (int)$minutes;
            $dateTime = DateTime::createFromFormat('H:i', sprintf('%02d:%02d', $hours, $minutes));

            if ($dateTime === false) {
                return false;
            }

            $totalMinutes = $hours * 60 + $minutes;

            if ($totalMinutes > 1440) {
                return false;
            }
        }

        return true;
    }

    private function saveTaskLog(array $taskTable, array $detail, string $employeeIdentifier): void
    {
        $existingTaskLog = ProjectManagementTaskLog::model()->findByAttributes([
            'day' => $detail['date'],
            'task_identifier' => $taskTable[self::ROW_IDENTIFIER],
            'employee_identifier' => $employeeIdentifier,
            'status' => Status::PUBLISHED,
        ]);

        if (array_key_exists('date', $detail)) {
            if (!is_null($existingTaskLog)) {
                $this->updateExistingTaskLog($existingTaskLog, $detail);
            } else {
                $this->createNewTaskLog($taskTable, $detail, $employeeIdentifier);
            }
        }
    }

    private function updateExistingTaskLog(ProjectManagementTaskLog $existingTaskLog, array $detail): void
    {
        $additionalData = json_decode($existingTaskLog->additional_data, true) ?? [];
        if (!empty($detail['comments']['internal'])) {
            $additionalData[ProjectManagementTaskLogEnum::TASK_LOG_INTERNAL_COMMENT] = $detail['comments']['internal'];
        }

        if (!empty($detail['comments']['external'])) {
            $additionalData[ProjectManagementTaskLogEnum::TASK_LOG_EXTERNAL_COMMENT] = $detail['comments']['external'];
        }

        $newAttributes = [
            'task_time' => ($detail['value'] ?? '00:00') . ':00',
            'additional_data' => json_encode($additionalData, JSON_UNESCAPED_UNICODE),
            'status' => Status::PUBLISHED,
        ];

        if ($this->hasChanges($existingTaskLog, $newAttributes)) {
            $existingTaskLog->setAttributes($newAttributes);
            $existingTaskLog->modified_by = userID() ?? 'system';
            $existingTaskLog->modified_on = date('Y-m-d H:i:s');
            $existingTaskLog->error_text = null;

            if ($existingTaskLog->validate()) {
                $existingTaskLog->save();
            }
        }
    }

    private function createNewTaskLog(array $taskTable, array $detail, string $employeeIdentifier): void
    {
        $additionalData = [];

        if (empty($detail['value'])) {
            return;
        }
        if (!empty($detail['comments']['internal'])) {
            $additionalData[ProjectManagementTaskLogEnum::TASK_LOG_INTERNAL_COMMENT] = $detail['comments']['internal'];
        }

        if (!empty($detail['comments']['external'])) {
            $additionalData[ProjectManagementTaskLogEnum::TASK_LOG_EXTERNAL_COMMENT] = $detail['comments']['external'];
        }

        $taskLog = new ProjectManagementTaskLog();
        $taskLog->day = $detail['date'];
        $taskLog->task_identifier = $taskTable[self::ROW_IDENTIFIER];
        $taskLog->employee_identifier = $employeeIdentifier;
        $taskLog->task_time = $detail['value'] . ':00';
        $taskLog->additional_data = json_encode($additionalData, JSON_UNESCAPED_UNICODE);
        $taskLog->status = Status::PUBLISHED;
        $taskLog->created_by = userID() ?? 'system';
        $taskLog->created_on = date('Y-m-d H:i:s');

        if ($taskLog->validate()) {
            $taskLog->save();
        }
    }

    private function hasChanges(ProjectManagementTaskLog $existingTaskLog, array $newAttributes): bool
    {
        foreach ($newAttributes as $key => $value) {
            if (is_string($value) && $this->isJson($value)) {
                $existingValue = json_decode($existingTaskLog->$key, true);
                $newValue = json_decode($value, true);
                if ($existingValue !== $newValue) {
                    return true;
                }
            } elseif ($existingTaskLog->$key !== $value) {
                return true;
            }
        }
        return false;
    }

    private function isJson($string): bool
    {
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }

    private function sendResponse(array $dayIds): void
    {
        $response = [
            'successCode' => 'OK',
        ];

        if (!empty($dayIds)) {
            header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
            $response = [
                'translations' => [],
                'errorCode' => 'validation',
                'correlationId' => '',
                'additionalInfo' => [
                    'validation' => [
                        'dayIds' => $dayIds,
                    ],
                ],
                'title' => Dict::getValue('project_management_project_page_title_error'),
                'detail' => Dict::getValue('project_management_project_page_title_detail'),
                'status' => 400,
                'instance' => '',
                'type' => 'SYSTEM',
            ];
        }

        Helper::sendJsonResponse($response);
    }

    private function sendResponseWithReason(string $reason): void
    {
        $response = [
            'successCode' => 'OK',
        ];

        if (!empty($dayIds)) {
            header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
            $response = [
                'translations' => [],
                'errorCode' => 'validation',
                'correlationId' => '',
                'additionalInfo' => [
                    'validation' => [
                        'reason' => $reason,
                    ],
                ],
                'title' => Dict::getValue('no_changes'),
                'detail' => Dict::getValue('no_changes'),
                'status' => 400,
                'instance' => '',
                'type' => 'SYSTEM',
            ];
        }

        Helper::sendJsonResponse($response);
    }

    /**
     * @throws Exception
     */
    private function registrationAndBalanceSave($dataToSave): array
    {
        if (is_null($dataToSave) || !isset($dataToSave[self::PROJECT_TABLE_COLUMNS])) {
            return [];
        }

        $dayIdsError = [];
        $times = $this->startAndEndTime($dataToSave[self::PROJECT_TABLE_COLUMNS]);
        $startDate = new DateTime($times['startDate']);
        $endDate = new DateTime($times['endDate']);

        $employeeContractId = $this->getEmployeeContractId(
            $times['identifier'],
            'EmployeeId',
            (new DateTime('now'))->format('Y-m-d')
        );
        if (is_null($employeeContractId)) {
            return [];
        }

        $transaction = Yang::app()->db->beginTransaction();
        try {
            $result = $this->registrationSave($times, $employeeContractId);
            $models = $result['models'];
            $dayIdsError = $result['dayTypeIdError'];
            $this->modelSave($models);
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollback();
            return [];
        }

        $this->runTimeCardCalculation($employeeContractId, $startDate, $endDate);

        $transaction = Yang::app()->db->beginTransaction();
        try {
            $result = $this->employeeCalcSave($times, $employeeContractId);
            $models = $result['models'];
            $dayIdsError = array_merge($dayIdsError, $result['dayTypeIdError']);
            $this->modelSave($models);
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollback();
        }

        return $dayIdsError;
    }

    /**
     * @throws Exception
     */
    private function employeeCalcSave(array $times, string $employeeContractId): array
    {
        $startDate = new DateTime($times['startDate']);
        $endDate = new DateTime($times['endDate']);

        $employeeCalc = $this->getEmployeeCalc($startDate, $endDate, [$employeeContractId]);
        $employeeCalcDays = $employeeCalc[$employeeContractId];
        $dayTypeIdError = [];
        $models = [];
        foreach ($times[self::BALANCE] as $balance) {
            if (is_null($balance['value'])) {
                $balance['value'] = '00:00';
            }
            if (!$this->balanceModifyCheck($employeeCalcDays, $balance)) {
                continue;
            }
            if (!$this->employeeCalcSaveCheck($employeeCalcDays, $balance['date'])) {
                $dayTypeIdError[] = $balance['id'];
                continue;
            }
            if (!$this->employeeCalcWorkTimeAndErrorCheck($employeeCalcDays, $balance['date'])) {
                $dayTypeIdError[] = $balance['id'];
                continue;
            }

            $filter = "`employee_contract_id` = '$employeeContractId' AND `day` = '" . $balance['date'] . "' AND `inside_type_id` LIKE 'bot%' AND `status` IN (" . Status::SAVED . ', ' . Status::PUBLISHED . ', ' . Status::DRAFT . ')';
            EmployeeCalc::model()->updateAll(
                ['status' => Status::INVALID, 'modified_by' => 'TimeTracker', 'modified_on' => date('Y-m-d H:i:s')],
                $filter
            );

            $filter = "`employee_contract_id` = '$employeeContractId' AND `day` = '" . $balance['date'] . "' AND `status` IN (" . Status::PUBLISHED . ', ' . Status::DRAFT . ', ' . Status::SAVED . ')';
            EmployeeCalc::model()->updateAll(
                ['status' => Status::LOCKED, 'modified_by' => 'TimeTracker', 'modified_on' => date('Y-m-d H:i:s')],
                $filter
            );

            $filter = "`employee_contract_id` = '$employeeContractId' AND `day` = '" . $balance['date'] . "' AND `status` IN (" . Status::SAVED . ', ' . Status::PUBLISHED . ', ' . Status::DRAFT . ')';
            EmployeeCalcUsedDaytype::model()->updateAll(
                ['status' => Status::LOCKED, 'modified_by' => 'TimeTracker', 'modified_on' => date('Y-m-d H:i:s')],
                $filter
            );

            $employeeCalc = new EmployeeCalc();
            $employeeCalc->employee_contract_id = $employeeContractId;
            $employeeCalc->day = $balance['date'];
            $employeeCalc->inside_type_id = 'botde';
            $employeeCalc->shift_type_id = 'de';
            $employeeCalc->is_next_day_calc = 0;
            $employeeCalc->is_prev_day_calc = 0;
            $employeeCalc->employee_calc_error_type_id = 0;
            $employeeCalc->cost_id = 'default';
            $employeeCalc->costcenter_id = 'default';
            $employeeCalc->value = $this->hisToSec($balance['value']);
            $employeeCalc->status = Status::LOCKED;
            $employeeCalc->created_by = 'TimeTracker';
            $employeeCalc->created_on = (new Datetime('now'))->format('Y-m-d');

            $models[] = $employeeCalc;
        }
        return ['models' => $models, 'dayTypeIdError' => $dayTypeIdError];
    }

    /**
     * @throws Exception
     */
    private function registrationSave(array $times, string $employeeContractId): array
    {
        $startDate = new DateTime($times['startDate']);
        $endDate = new DateTime($times['endDate']);
        $dayTypeIdError = [];
        $employeeCalc = $this->getEmployeeCalc(
            $startDate,
            $endDate,
            [$employeeContractId],
        );
        $employeeCalcDays = $employeeCalc[$employeeContractId];
        $emDayR = new GetEmployeeCalcUsedDaytypeRegs();
        $usedDayTypeMinMaxRegs = $emDayR->getEcudMinMaxRegs(
            $startDate->format('Y-m-d'),
            $endDate->format('Y-m-d'),
            [$employeeContractId]
        );
        $card = EmployeeCard::getCardByEcId($employeeContractId, "'" . $startDate->format('Y-m-d') . "'");
        $models = [];
        foreach ($times[self::REGS] as $regKey => $regValue) {
            if (!isset($usedDayTypeMinMaxRegs[$employeeContractId][$regKey])) {
                continue;
            }
            if (empty($regValue['startTime']) && !empty($regValue['endTime'])) {
                $dayTypeIdError[] = $regValue['startTimeId'];
                continue;
            }
            if (!empty($regValue['startTime']) && empty($regValue['endTime'])) {
                $dayTypeIdError[] = $regValue['endTimeId'];
                continue;
            }

            $usedDayTypeMinReg = $usedDayTypeMinMaxRegs[$employeeContractId][$regKey];
            $usedDayTypeMinReg = reset($usedDayTypeMinReg);
            $startTime = $this->createDateTime('Y-m-d H:i', $regKey . ' ' . $regValue['startTime']);
            $endTime = $this->createDateTime('Y-m-d H:i', $regKey . ' ' . $regValue['endTime']);
            $modifiedRegKey = $regKey;

            if (!is_null($startTime) && !is_null($endTime) &&
                $startTime->getTimestamp() > $endTime->getTimestamp()) {
                $modifiedRegKey = $endTime->modify('+1 day')->format('Y-m-d');
            }

            if (isset($usedDayTypeMinReg['minreg'], $regValue['startTime'])) {
                $minRegMod = $this->registrationModifyCheck($usedDayTypeMinReg['minreg'], $regValue['startTime']);
            } elseif (isset($regValue['startTime'])) {
                $minRegMod = $this->addRegistration($regKey, $regValue['startTime']);
            } else {
                $minRegMod = [];
            }
            if (isset($usedDayTypeMinReg['maxreg'], $regValue['endTime'])) {
                $maxRegMod = $this->registrationModifyCheck($usedDayTypeMinReg['maxreg'], $regValue['endTime']);
            } elseif (isset($regValue['endTime'])) {
                $maxRegMod = $this->addRegistration($modifiedRegKey, $regValue['endTime']);
            } else {
                $maxRegMod = [];
            }

            if (empty($minRegMod) && empty($maxRegMod)) {
                continue;
            }

            if (!$this->employeeCalcSaveCheck($employeeCalcDays, $regKey)) {
                $dayTypeIdError[] = $regValue['startTimeId'];
                $dayTypeIdError[] = $regValue['endTimeId'];
                continue;
            }
            if (!empty($minRegMod)) {
                if (!is_null($minRegMod['newRegTime'])) {
                    $models[] = $this->newRegModel($minRegMod['newRegTime']->format('Y-m-d H:i:s'), (string)$card[0], 'NMB');
                }
                if (!is_null($minRegMod['regTime'])) {
                    $delReg = $minRegMod['regTime']->format('Y-m-d H:i:s');
                    $filter = "`time` = '$delReg' AND `card` = '$card[0]' AND `status` = " . Status::PUBLISHED;
                    Registration::model()->updateAll(
                        [
                            'status' => Status::DELETED,
                            'modified_by' => 'TimeTracker',
                            'modified_on' => date('Y-m-d H:i:s'),
                        ],
                        $filter
                    );
                }
            }
            if (!empty($maxRegMod)) {
                if (!is_null($maxRegMod['newRegTime'])) {
                    $models[] = $this->newRegModel($maxRegMod['newRegTime']->format('Y-m-d H:i:s'), (string)$card[0], 'NMK');
                }
                if (!is_null($maxRegMod['regTime'])) {
                    $delReg = $maxRegMod['regTime']->format('Y-m-d H:i:s');
                    $filter = "`time` = '$delReg' AND `card` = '$card[0]' AND `status` = " . Status::PUBLISHED;
                    Registration::model()->updateAll(
                        [
                            'status' => Status::DELETED,
                            'modified_by' => 'TimeTracker',
                            'modified_on' => date('Y-m-d H:i:s'),
                        ],
                        $filter
                    );
                }
            }
        }

        return ['models' => $models, 'dayTypeIdError' => $dayTypeIdError];
    }

    private function checkLogin(): bool
    {
        if (empty(Yang::getUserID())) {
            Yang::log(Dict::getValue('login_need'));
            return false;
        }

        return true;
    }

    private function hisToSec(string $his): int
    {
        $isNegative = false;

        if (str_starts_with($his, '-')) {
            $isNegative = true;
            $his = substr($his, 1);
        }

        sscanf($his, '%d:%d', $hours, $minutes);
        $seconds = $hours * 60 * 60 + $minutes * 60;

        return $isNegative ? -$seconds : $seconds;
    }

    private function balanceModifyCheck(array $employeeCalcDays, array $balance): bool
    {
        if (!isset($employeeCalcDays[$balance['date']])) {
            return false;
        }
        $employeeCalcDay = $employeeCalcDays[$balance['date']];
        if (empty($employeeCalcDay)) {
            return false;
        }

        $calcBalance = null;
        $szaldo = ['botde', 'botdu1', 'botdu2', 'botej'];
        foreach ($employeeCalcDay as $calc) {
            if (in_array($calc['inside_type_id'], $szaldo)) {
                $calcBalance = $calc['value'];
            }
        }
        return $calcBalance != $this->hisToSec($balance['value']);
    }

    private function modelSave($models): void
    {
        foreach ($models as $model) {
            if ($model->validate()) {
                $model->save();
            }
        }
    }

    private function modelsToArray(array $models): array
    {
        return array_map(function ($dynamicRow) {
            return $dynamicRow->getAttributes();
        }, $models);
    }

    private function newRegModel(string $time, string $card, string $eventTypeId): Registration
    {
        $newRegModel = new Registration();
        $newRegModel->time = $time;
        $newRegModel->status = Status::PUBLISHED;
        $newRegModel->created_by = 'TimeTracker';
        $newRegModel->created_on = (new Datetime('now'))->format('Y-m-d');
        $newRegModel->card = $card;
        $newRegModel->terminal_id = 'default';
        $newRegModel->reader_id = 'default';
        $newRegModel->event_type_id = $eventTypeId;
        $newRegModel->calc_status = 0;
        $newRegModel->reg_status = 2;
        $newRegModel->random_selected = 0;

        return $newRegModel;
    }

    private function getEmployeeContractId(string $identifier, string $identifierType, string $date): ?string
    {
        $result = '';
        switch ($identifierType) {
            case 'EmployeeContractId':
                $result = $identifier;
                break;
            case 'EmployeeId' :
                $result = EmployeeContract::getEmployeeContractIdByEmployeeId($identifier, $date);
                break;
            case 'EmpId':
                $result = EmployeeContract::getEcIDByEmpId($identifier);
        }
        return $result;
    }

    private function registrationModifyCheck(string $regTime, string $newRegTime): array
    {
        $reg = $this->createDateTime('Y-m-d H:i:s', $regTime);
        if (is_null($reg)) {
            return [];
        }
        /** @var DateTimeImmutable $newReg */
        $newReg = $reg->modify($newRegTime);

        if (!is_null($reg) && !is_null($newReg)) {
            $regDiff = $reg->diff($newReg);
            if ($regDiff->h == 0 && $regDiff->i == 0) {
                return [];
            }
        }
        $result['regTime'] = $reg;
        $result['newRegTime'] = $newReg;
        return $result;
    }

    private function addRegistration(string $regKey, string $newRegTime): array
    {
        $reg = $this->createDateTime('Y-m-d H:i', $regKey . ' ' . $newRegTime);
        /** @var DateTimeImmutable $newReg */
        $newReg = $reg->modify($regKey);

        $result['newRegTime'] = $newReg;
        return $result;
    }

    private function createDateTime(string $format, ?string $time): ?DateTimeImmutable
    {
        $result = DateTimeImmutable::createFromFormat($format, (string)$time);
        return $result === false ? null : $result;
    }

    private function startAndEndTime(array $times): array
    {
        $result = [];
        $startTime = [];
        $endTime = [];
        $balance = [];
        $identifier = '';
        foreach ($times as $column) {
            switch ($column['rowIdentifier']) {
                case 'start_time' :
                    $startTime = $column['details'];
                    $identifier = $column['employeeIdentifier'];
                    break;
                case 'end_time' :
                    $endTime = $column['details'];
                    break;
                case 'balance' :
                    $balance = $column['details'];
                    break;
            }
        }

        foreach ($startTime as $time) {
            if ($time['id'] == 'start_time_name' || !array_key_exists('date', $time)) {
                continue;
            }
            $result[self::REGS][$time['date']]['startTimeId'] = $time['id'];
            $result[self::REGS][$time['date']]['startTime'] = $time['value'];
        }

        foreach ($endTime as $time) {
            if ($time['id'] == 'end_time_name' || !array_key_exists('date', $time)) {
                continue;
            }
            $result[self::REGS][$time['date']]['endTimeId'] = $time['id'];
            $result[self::REGS][$time['date']]['endTime'] = $time['value'];
        }
        $result[self::EMPID] = $identifier;
        $result['startDate'] = array_key_first($result[self::REGS]);
        $result['endDate'] = array_key_last($result[self::REGS]);
        $result[self::BALANCE] = $balance;
        return $result;
    }

    private function employeeCalcSaveCheck(?array $employeeCalcDays, string $date): bool
    {
        $employeeCalcDay = $employeeCalcDays[$date] ?? [];
        $employeeCalcDay = reset($employeeCalcDay);
        return !(isset($employeeCalcDay['status']) && in_array(
                $employeeCalcDay['status'],
                [Status::LOCKED]
            ));
    }

    private function employeeCalcWorkTimeAndErrorCheck(?array $employeeCalcDays, string $date): bool
    {
        $employeeCalcDay = $employeeCalcDays[$date] ?? [];
        foreach ($employeeCalcDay as $row => $employeeCalcData) {
            if ($employeeCalcData['employee_calc_error_type_id'] != 0) {
                return false;
            }
        }
        return true;
    }

    private function isAbsence(array $attributes): bool
    {
        return !is_null($attributes['absence_name']);
    }

    private function getAbsenceName(array $attributes): ?string
    {
        return $attributes['absence_name'];
    }

    private function isStandby(array $attributes): bool
    {
        if (is_null($attributes['standby_start']) || is_null($attributes['standby_end'])) {
            return false;
        }

        if ($attributes['standby_start'] === $attributes['standby_end']) {
            return false;
        }

        return true;
    }

    private function getStandbyName(array $attributes): ?string
    {
        $isStandby = !is_null($attributes['standby_start']) && !is_null($attributes['standby_end']);
        $name = $isStandby ? Dict::getModuleValue('ttwa-base', 'long_standby') : null;

        return $name;
    }

    private function getDayTypeName(array $attributes): string
    {
        if (!empty($attributes[self::USED_DAYTYPE_NAME])) {
            return $attributes[self::USED_DAYTYPE_NAME];
        }

        return Dict::getValue('ptr_error_schedule_missing');
    }

    private function getBreakTime(array $attributes): ?string
    {
        $breakTimes = 0;
        for ($i = 1; $i <= 5; $i++) {
            if ((int)$attributes["used_paid_break_time_$i"] === 0) {
                $breakTimes += (int)$attributes["used_break_time_duration_$i"];
            }
        }

        return !empty($breakTimes) ? secToHis($breakTimes) : null;
    }

    private function runTimeCardCalculation(string $employeeContractId, DateTime $startDate, DateTime $endDate): void
    {
        $timeCardCalculation = new TimeCardCalculation([$employeeContractId],
            $startDate->format('Y-m-d'),
            $endDate->format('Y-m-d'),
            false);
        $timeCardCalculation->payrollcalculation();
    }
}
