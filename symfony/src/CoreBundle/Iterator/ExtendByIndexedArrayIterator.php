<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Iterator;

final readonly class ExtendByIndexedArrayIterator implements \Iterator
{
    private iterable $innerIterator;
    private array $indexedArray;
    private string $key;

    public function __construct(iterable $innerIterator, array $indexedArray, string $key)
    {
        $this->innerIterator = $innerIterator;
        $this->indexedArray = $indexedArray;
        $this->key = $key;
    }

    public function current(): mixed
    {
        $current = $this->innerIterator->current();

        $lookupKey = $current[$this->key] ?? null;
        if ($lookupKey !== null && array_key_exists($lookupKey, $this->indexedArray)) {
            return array_merge($current, $this->indexedArray[$lookupKey]);
        }

        return $current;
    }

    public function next(): void
    {
        $this->innerIterator->next();
    }

    public function key(): mixed
    {
        return $this->innerIterator->key();
    }

    public function valid(): bool
    {
        return $this->innerIterator->valid();
    }

    public function rewind(): void
    {
        $this->innerIterator->rewind();
    }
}