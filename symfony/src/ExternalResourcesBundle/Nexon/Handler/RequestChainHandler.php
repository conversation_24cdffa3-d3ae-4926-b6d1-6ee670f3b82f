<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Handler;

use LoginAutonom\CoreBundle\Iterator\ExtendByIndexedArrayIterator;
use LoginAutonom\CoreBundle\Util\ArrayUtil;
use LoginAutonom\ExternalResourcesBundle\DTO\ApiConfigurationDTO;
use LoginAutonom\ExternalResourcesBundle\DTO\ProcessingContextDTO;
use LoginAutonom\ExternalResourcesBundle\DTO\RequestParametersContextDTO;
use LoginAutonom\ExternalResourcesBundle\Interfaces\RequestHandlerInterface;
use LoginAutonom\ExternalResourcesBundle\Nexon\Builder\ChainStorageResultDTOBuilder;
use LoginAutonom\ExternalResourcesBundle\Nexon\Builder\RequestParametersDTOBuilder;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiRequestConfigurationKeyEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiExtractorConfigurationKeyEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiResponseValueEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Guesser\ChainConfigParamsGuesser;
use LoginAutonom\ExternalResourcesBundle\Nexon\Guesser\DateParamGuesser;
use LoginAutonom\ExternalResourcesBundle\Nexon\Guesser\DynamicParamsGuesser;
use LoginAutonom\ExternalResourcesBundle\Nexon\Guesser\RequestSpecificParamsGuesser;
use LoginAutonom\ExternalResourcesBundle\Nexon\Util\FieldMappingUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;
use Symfony\Component\DependencyInjection\Attribute\TaggedLocator;
use Symfony\Contracts\Service\ServiceProviderInterface;

#[Autoconfigure]
final class RequestChainHandler implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    public function __construct(
        #[TaggedLocator(RequestHandlerInterface::TAG, defaultIndexMethod: 'type')]
        private readonly ServiceProviderInterface $requests,
        private readonly RequestParametersDTOBuilder $parameterBuilder,
        private readonly ChainStorageResultDTOBuilder $chainStorageResultDTOBuilder,
        private readonly DateParamGuesser $dateParamGuesser,
        private readonly RequestSpecificParamsGuesser $requestSpecificParamsGuesser,
        private readonly ChainConfigParamsGuesser $chainConfigParamsGuesser,
        private readonly DynamicParamsGuesser $dynamicParamsGuesser,
        private readonly FieldMappingUtil $fieldMappingUtil,
        private readonly ArrayUtil $arrayUtil,
    ) {
    }

    public function handle(
        array $requestChains,
        array $options,
        ApiConfigurationDTO $apiConfiguration
    ): array {
        $storageResultDTO = $this->chainStorageResultDTOBuilder
            ->reset()
            ->build();

        $ideiglenesStorage = [];

        $requestChains = $this->sortRequestChainsByOrder($requestChains);
        foreach ($requestChains as $chainConfig) {
            $requestType = $chainConfig[ApiRequestConfigurationKeyEnum::TYPE->value] ?? $chainConfig;
            if (!$this->requests->has($requestType)) {
                $this->logger->warning('Request handler not found for type: {type}', ['type' => $requestType]);
                continue;
            }

            if ($storageResultDTO->hasResultForType($requestType)) {
                continue;
            }

            try {
                $processingContext = new ProcessingContextDTO(
                    $requestType,
                    $chainConfig,
                    $options,
                    $apiConfiguration,
                    $storageResultDTO->toArray()
                );

                $processedResults = $this->processRequest($processingContext);
                if (!empty($chainConfig[ApiRequestConfigurationKeyEnum::MERGE_TO_RESULT->value] ?? false)) {
                    $existingResults = $storageResultDTO->toArray() ?? [];
                    $mergeKey = $chainConfig[ApiRequestConfigurationKeyEnum::GROUP_BY->value] ?? $requestType;

                    // Flatten grouped results if they are grouped
                    $flattenedResults = $this->flattenGroupedResults($processedResults);

                    $iterator = new ExtendByIndexedArrayIterator(
                        new \ArrayIterator($flattenedResults),
                        $ideiglenesStorage,
                        $mergeKey
                    );
                    $processedResults = iterator_to_array($iterator);
                }
                $storageResultDTO = $storageResultDTO->withResult($requestType, $processedResults);
                $ideiglenesStorage = $processedResults;
            } catch (\Exception $e) {
                $this->logger?->error('Error processing request type {type}: {message}', [
                    'type' => $requestType,
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
                throw $e;
            }
        }

        return $storageResultDTO->toArray();
    }

    private function processRequest(ProcessingContextDTO $context): array
    {
        /** @var RequestHandlerInterface $request */
        $request = $this->requests->get($context->getRequestType());

        $parametersContext = new RequestParametersContextDTO(
            $context->getOptions(),
            $context->getRequestType(),
            $context->getChainConfig(),
            $context->getResultStorage()
        );

        $requestParams = $this->getRequestParams($parametersContext);
        $requestRawData = $request->handle($context->getApiConfiguration(), $requestParams);
        $processedData = $this->processResponseData($requestRawData, $context);

        if (isset($context->getChainConfig()[ApiRequestConfigurationKeyEnum::GROUP_BY->value])) {
            $groupByKey = $context->getChainConfig()[ApiRequestConfigurationKeyEnum::GROUP_BY->value];
            $processedData = ArrayUtil::groupBy(
                $processedData,
                fn($item) => $item[$groupByKey] ?? null
            );
        }

        return $processedData;
    }

    private function processResponseData(mixed $rawData, ProcessingContextDTO $context): array
    {
        $requestType = $context->getRequestType();
        $options = $context->getOptions();
        $data = [];

        if (empty($rawData)) {
            return $data;
        }

        if (is_array($rawData)) {
            $data = $rawData[ApiResponseValueEnum::VALUE->value] ?? $rawData;
        } elseif (is_string($rawData)) {
            $data = json_decode($rawData, true)[ApiResponseValueEnum::VALUE->value] ?? json_decode($rawData, true);
        }

        if (isset($options[ApiExtractorConfigurationKeyEnum::FIELD_MAPPINGS->value][$requestType])) {
            $mappings = $options[ApiExtractorConfigurationKeyEnum::FIELD_MAPPINGS->value][$requestType];
            $data = $this->fieldMappingUtil->applyFieldMappings($data, $mappings);
        }

        return $data;
    }

    private function getRequestParams(RequestParametersContextDTO $context): array
    {
        return $this->parameterBuilder
            ->reset()
            ->setDateParameters(
                $this->dateParamGuesser->guess(
                    $context->getChainConfig()
                )
            )
            ->setRequestSpecificParameters(
                $this->requestSpecificParamsGuesser->guess(
                    $context->getRequestType(),
                    $context->getOptions()
                )
            )
            ->setChainConfigParameters(
                $this->chainConfigParamsGuesser->guess($context->getChainConfig())
            )
            ->setDynamicParameters(
                $this->dynamicParamsGuesser->guess(
                    $context->getChainConfig(),
                    $context->getResultStorage()
                )
            )
            ->build()
            ->toArray();
    }

    private function sortRequestChainsByOrder(array $requestChains): array
    {
        return ArrayUtil::sortByKey($requestChains, ApiRequestConfigurationKeyEnum::ORDER->value, 'ASC');
    }

    private function flattenGroupedResults(array $results): array
    {
        $flattened = [];
        foreach ($results as $group) {
            if (is_array($group)) {
                $flattened = array_merge($flattened, array_values($group));
            } else {
                $flattened[] = $group;
            }
        }
        return $flattened;
    }
}
