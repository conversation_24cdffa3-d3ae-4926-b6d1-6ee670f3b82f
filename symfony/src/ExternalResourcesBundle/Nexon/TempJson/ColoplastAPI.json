{"type": "simple-workflow", "config": {"flows": {"employee-data-sync": {"load": {"type": "entities-from-row-entry", "config": {"fieldName": [], "persistReasons": [], "lastRowIdentifiers": []}}, "type": "employee-data-sync", "extract": {"type": "nexon-provider-api-based-extractor", "config": {"rowLimit": 100, "apiConfig": {"protocol": "env(\"API_PROTOCOL\")", "hostname": "env(\"API_HOST\")", "port": null, "path": null, "authConfigs": [{"type": "basic", "config": {"username": "env(\"API_USER\")", "password": "env(\"API_PASSWORD\")", "auth-type": "basic"}}]}, "fieldMappings": {"hr-relationship": {"HrRelationshipId": "hrRelationshipId", "PersonId": "personId"}, "employment-contract": {"EmploymentContractId": "employmentContractId", "HrRelationshipId": "hrRelationshipId"}, "employment-contract-version": {"EmploymentContractId": "employmentContractId", "WorkTimePerWorkDayInMinutes": "workTimePerWorkDayInMinutes", "ValidFrom": "validFrom", "ValidTo": "validTo"}, "get-persons-2": {"PersonId": "personId", "EmployeeNumber": "employeeNumber"}}, "requests": [{"order": 0, "type": "hr-relationship", "groupBy": "hrRelationshipId", "mergeToResult": true}, {"order": 1, "type": "employment-contract", "dynamicParams": {"hrRelationshipId": {"extractValueToParams": false, "fromRequest": "hr-relationship", "field": "hrRelationshipId"}}, "groupBy": "hrRelationshipId", "mergeToResult": true}, {"order": 2, "type": "employment-contract-version", "dynamicParams": {"employmentContractId": {"extractValueToParams": false, "fromRequest": "employment-contract", "field": "employmentContractId"}}, "groupBy": "employmentContractId", "mergeToResult": true}, {"order": 3, "type": "get-persons-2", "dynamicParams": {"personId": {"extractValueToParams": false, "fromRequest": "hr-relationship", "field": "personId"}}, "groupBy": "personId", "mergeToResult": true}], "extraFields": {}}}, "transform": [{"type": "add-technical-fields-to-row-transformer", "config": {}}], "entityCache": []}}, "flowOrder": {"employee-data-sync": 1}, "preProcess": [], "postProcess": [], "transitions": []}}